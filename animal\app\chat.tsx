import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Image,
  Alert,
} from 'react-native';
import { useTranslation } from '@/hooks/useTranslation';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Send, Paperclip, Mic, X } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
import { useThemeColors } from '@/hooks/useThemeColors';
import { useAuthStore } from '@/store/auth-store';
import { useFarmStore } from '@/store/farm-store';
import { useLookupStore } from '@/store/lookup-store';
import { useFocusEffect } from '@react-navigation/native';

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
  image?: string;
  isLoading?: boolean;
  animalInfo?: {
    name: string;
    species: string;
    id: string;
  };
  animalImages?: Array<{
    imageUri: string;
    name: string;
    species: string;
    id: string;
  }>;
  farmImages?: Array<{
    imageUri: string;
    name: string;
    location: string;
    id: string;
  }>;
  expenseImages?: Array<{
    imageUri: string | null;
    name: string;
    amount: string;
    date: string;
    paymentMethod: string;
    description: string;
    id: string;
    category: string;
    fullAmount: number;
    currency: string;
  }>;
  employeeSelection?: {
    employees: Array<{
      id: string;
      name: string;
      role: string;
      imageUri?: string;
    }>;
    taskContext: any;
  };
  farmSelection?: {
    farms: Array<{
      id: string;
      name: string;
      location: string;
      imageUri?: string;
    }>;
    context: any;
  };
  taskSelection?: {
    tasks: Array<{
      id: string;
      label: string;
      description: string;
      priority: string;
      status: string;
      dueDate?: number;
      assigneeName?: string;
    }>;

  moduleImages?: Array<{
    imageUri: string | null;
    name: string;
    description: string;
    id: string;
    icon: string;
  }>;
   
    
    context: any;
  };
  context?: any; // General context for preserving state across interactions
}

export default function ChatScreen() {
  const { t, language } = useTranslation();
  const themedColors = useThemeColors();
  const { user } = useAuthStore();
  const { farms, fetchFarms, getSelectedFarm } = useFarmStore();
  const { getLookupsByCategoryParsedData } = useLookupStore();
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [lastAnimalData, setLastAnimalData] = useState<any | null>(null);
  const [lastFarmData, setLastFarmData] = useState<any | null>(null);
  const [healthCheckContext, setHealthCheckContext] = useState<any | null>(null);
  const flatListRef = useRef<FlatList>(null);

  // Employee selection state (for inline display)
  const [pendingTaskContext, setPendingTaskContext] = useState<any>(null);

  // Refresh farms when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      if (user?.id) {
        fetchFarms(user.id);
        console.log(farms)
      }
    }, [user?.id, fetchFarms])
  );

  // Get lookup data at component level
  const farmTypes = getLookupsByCategoryParsedData('farmType', 'farms.farmType.');
  const farmStatuses = getLookupsByCategoryParsedData('farmStatus', 'farms.farmStatus.');
  const sizeUnits = getLookupsByCategoryParsedData('areaUnit', 'farms.areaUnit.');

  // Calculate default IDs at component level
  const defaultFarmType = farmTypes.find(item => item.id === 'livestock') || farmTypes[0];
  const defaultStatus = farmStatuses.find(item => item.label.toLowerCase() === 'active') || farmStatuses[0];
  const defaultSizeUnit = sizeUnits.find(item => item.label.toLowerCase() === 'acre') || sizeUnits[0];

  const sendMessage = async () => {
    if (!inputText.trim() && !selectedImage) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: inputText.trim() || (selectedImage ? t('chat.imageAnalysis') : ''),
      isUser: true,
      timestamp: new Date(),
      image: selectedImage || undefined,
    };
    setMessages(prev => [...prev, userMessage]);
    const messageText = inputText.trim();
    const imageToAnalyze = selectedImage;
    setInputText('');
    setSelectedImage(null);
    setIsLoading(true);

    // Add loading message with animated dots
    const loadingMessage: Message = {
      id: (Date.now() + 0.5).toString(),
      text: '...',
      isUser: false,
      timestamp: new Date(),
      isLoading: true,
    };

    setMessages(prev => [...prev, loadingMessage]);

    try {
      // Use the lookup data calculated at component level
      const selectedFarm = getSelectedFarm();
      const requestBody: any = {
        language: language,
        userId: user?.id,
        farms: selectedFarm ? [{
          id: selectedFarm.id,
          name: selectedFarm.name,
          location: selectedFarm.location
        }] : farms?.map(farm => ({
          id: farm.id,
          name: farm.name,
          location: farm.location
        })),
        previousAnimalData: lastAnimalData,
        previousFarmData: lastFarmData, // Add farm data
        defaultFarmTypeId: defaultFarmType?.id || "B2bye8PZBQYqscxXoVqZ",
        defaultStatusId: defaultStatus?.id || "tEbttSFNlpr6gGm5y66l",
        defaultSizeUnitId: defaultSizeUnit?.id || "QmzgdLcdPP0iEFVT5LyP",
        farmLocation: "", // Can be extracted from chat context if needed
        context: healthCheckContext // Pass health check context for animal selection and saving
      };

      if (imageToAnalyze) {
        requestBody.imageUri = imageToAnalyze;
        requestBody.prompt = messageText || (language === 'ur'
          ? 'اس تصویر کا تجزیہ کریں'
          : 'Analyze this image');
      } else {
        requestBody.prompt = messageText;
      }

      // const response = await fetch('https://animalapp-3tfznjgouq-ts.a.run.app/open-ai-chat', {
      const response = await fetch('http://127.0.0.1:5001/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });
      console.log("Sherry", response)


      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Remove loading message
      setMessages(prev => prev.filter(msg => !msg.isLoading));

      // Check for employee selection requirement
      if (data.needsEmployeeSelection && data.employeeList) {
        console.log('🧑‍💼 Employee selection required:', data.employeeList.length, 'employees');

        // Store task context for later use
        setPendingTaskContext(data.context);

        // Create message with employee selection
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          text: data.message,
          isUser: false,
          timestamp: new Date(),
          employeeSelection: {
            employees: data.employeeList.map((emp: any) => ({
              id: emp.id,
              name: emp.label || emp.name,
              role: emp.role,
              imageUri: emp.imageUri
            })),
            taskContext: data.context
          },
          context: data.context || undefined
        };
        setMessages(prev => [...prev, aiMessage]);

        return; // Don't process further
      }

      // Check for task selection requirement
      if (data.needsTaskSelection && data.taskList) {
        console.log('🗑️ Task selection required:', data.taskList.length, 'tasks');

        // Create message with task selection
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          text: data.message,
          isUser: false,
          timestamp: new Date(),
          taskSelection: {
            tasks: data.taskList.map((task: any) => ({
              id: task.id,
              label: task.label,
              description: task.description,
              priority: task.priority,
              status: task.status,
              dueDate: task.dueDate,
              assigneeName: task.assigneeName
            })),
            context: data.context
          },
          context: data.context || undefined
        };
        setMessages(prev => [...prev, aiMessage]);
        return; // Don't process further
      }

      if (data.message) {
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          text: data.message,
          isUser: false,
          timestamp: new Date(),
          image: data.image || undefined, // Include image if present (for health checks)
          animalInfo: data.animalInfo || undefined, // Include animal info if present
          animalImages: data.animalImages || undefined, // Include all animal images if present
          farmImages: data.farmImages || undefined, // Include all farm images if present
          expenseImages: data.expenseImages || undefined, // Include all expense images if present
          farmSelection: data.needsFarmSelection ? {
            farms: data.farmList || [],
            context: data.context
          } : undefined,
          context: data.context || undefined, // Preserve context for animal deletion and other operations
        };
        setMessages(prev => [...prev, aiMessage]);

        // Handle different data types
        if (data.animalData) {
          setLastAnimalData(data.animalData);
          console.log('🐄 EXTRACTED ANIMAL DATA FOR DATABASE:');
          console.log(JSON.stringify(data.animalData, null, 2));
        }

        if (data.farmData) {
          setLastFarmData(data.farmData); // Store farm data
          console.log('🏡 EXTRACTED FARM DATA FOR DATABASE:');
          console.log(JSON.stringify(data.farmData, null, 2));
        }

        if (data.expenseData) {
          console.log('🧾 EXTRACTED EXPENSE DATA FOR DATABASE:');
          console.log(JSON.stringify(data.expenseData, null, 2));
        }

        // Handle health check context for animal selection and saving
        if (data.context) {
          setHealthCheckContext(data.context);
          console.log('🏥 HEALTH CHECK CONTEXT:', data.context);
        }

        // Log animal info for health checks
        if (data.animalInfo) {
          console.log('🐄 HEALTH CHECK ANIMAL INFO:', data.animalInfo);
        }

        // Clear context after successful operations
        if (data.healthCheckSaved || data.saved || data.deleted) {
          setHealthCheckContext(null);
          console.log('✅ Operation completed, context cleared');
        }
      } else {
        throw new Error('No message in response');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      // Remove loading message
      setMessages(prev => prev.filter(msg => !msg.isLoading));

      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: t('chat.errorMessage'),
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleImagePicker = async () => {
    try {
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (!permissionResult.granted) {
        Alert.alert('Permission Required', 'Please grant permission to access your photo library.');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setSelectedImage(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to select image. Please try again.');
    }
  };

  const handleVoicePress = () => {
    console.log('coming soon');
  };

  const removeSelectedImage = () => {
    setSelectedImage(null);
  };

  // Handle employee selection for task assignment
  const handleEmployeeSelection = async (employeeId: string, employeeName: string, taskContext: any) => {
    if (!taskContext || !employeeId) return;

    console.log('🧑‍💼 Employee selected:', employeeId, employeeName);
    setIsLoading(true);

    // Add user message showing selection
    const userMessage: Message = {
      id: Date.now().toString(),
      text: `Selected: ${employeeName}`,
      isUser: true,
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, userMessage]);

    // Add loading message
    const loadingMessage: Message = {
      id: (Date.now() + 0.5).toString(),
      text: '...',
      isUser: false,
      timestamp: new Date(),
      isLoading: true,
    };
    setMessages(prev => [...prev, loadingMessage]);

    try {
      const requestBody = {
        prompt: employeeId, // Send employee ID as prompt for compatibility
        selectedEmployeeId: employeeId,
        userId: user?.id,
        language: language,
        context: taskContext,
        farms: farms.map(farm => ({
          id: farm.id,
          name: farm.name,
          ownerId: farm.ownerId
        }))
      };

      console.log('📤 Sending employee selection request:', requestBody);

      const response = await fetch('http://127.0.0.1:5001/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Server error response:', errorText);
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Remove loading message
      setMessages(prev => prev.filter(msg => !msg.isLoading));
      setIsLoading(false);

      if (data.saved) {
        // Task created successfully
        const successMessage: Message = {
          id: (Date.now() + 1).toString(),
          text: data.message,
          isUser: false,
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, successMessage]);

        // Clear task context
        setPendingTaskContext(null);
        setHealthCheckContext(null);

        console.log('✅ Task created successfully, contexts cleared:', data.databaseId);
      } else {
        // Other response
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          text: data.message || 'Unexpected response',
          isUser: false,
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, aiMessage]);
      }

    } catch (error) {
      console.error('❌ Employee selection failed:', error);
      setIsLoading(false);
      setMessages(prev => prev.filter(msg => !msg.isLoading));

      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: t('chat.errorMessage') || 'Failed to assign task. Please try again.',
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    }
  };

  // Handle task selection for deletion
  const handleTaskSelection = async (taskId: string, taskName: string, taskContext: any) => {
    if (!taskContext || !taskId) return;

    console.log('🗑️ Task selected for deletion:', taskId, taskName);
    setIsLoading(true);

    // Add user message showing selection
    const userMessage: Message = {
      id: Date.now().toString(),
      text: `Selected: ${taskName}`,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);

    // Add loading message
    const loadingMessage: Message = {
      id: (Date.now() + 1).toString(),
      text: t('chat.processing') || 'Processing...',
      isUser: false,
      timestamp: new Date(),
      isLoading: true,
    };

    setMessages(prev => [...prev, loadingMessage]);

    try {
      const requestBody = {
        prompt: taskId, // Send task ID as prompt for compatibility
        selectedTaskId: taskId,
        userId: user?.id,
        language: language,
        context: taskContext,
        farms: farms.map(farm => ({
          id: farm.id,
          name: farm.name,
          location: farm.location
        }))
      };

      console.log('📤 Sending task selection request:', requestBody);

      const response = await fetch('http://127.0.0.1:5001/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      const data = await response.json();
      console.log('📥 Task selection response:', data);

      // Remove loading message
      setMessages(prev => prev.filter(msg => !msg.isLoading));
      setIsLoading(false);

      if (data.deleted) {
        // Task deleted successfully
        const successMessage: Message = {
          id: (Date.now() + 1).toString(),
          text: data.message,
          isUser: false,
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, successMessage]);

        console.log('✅ Task deleted successfully:', data.databaseId);
      } else {
        // Other response
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          text: data.message,
          isUser: false,
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, aiMessage]);
      }

    } catch (error) {
      console.error('❌ Task selection failed:', error);
      setIsLoading(false);
      setMessages(prev => prev.filter(msg => !msg.isLoading));

      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: t('chat.errorMessage') || 'Failed to delete task. Please try again.',
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    }
  };

  const handleAnimalSelection = async (animalId: string, animalName: string) => {
    // Find the context from the most recent message with animalImages
    const lastMessageWithContext = messages
      .slice()
      .reverse()
      .find(msg => msg.animalImages && msg.context);

    const contextToSend = lastMessageWithContext?.context || {};

    console.log('🔍 Found context for animal selection:', contextToSend);

    // Send the animal ID as a message to select the animal
    const userMessage: Message = {
      id: Date.now().toString(),
      text: `Selected: ${animalName}`,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    // Add loading message
    const loadingMessage: Message = {
      id: (Date.now() + 0.5).toString(),
      text: '...',
      isUser: false,
      timestamp: new Date(),
      isLoading: true,
    };

    setMessages(prev => [...prev, loadingMessage]);

    try {
      const requestBody = {
        prompt: animalId, // Send animal ID as prompt for compatibility
        selectedAnimalId: animalId,
        userId: user?.id,
        language: language,
        context: contextToSend, // Use the preserved context instead of empty object
        farms: farms.map(farm => ({
          id: farm.id,
          name: farm.name,
          ownerId: farm.ownerId
        }))
      };

      console.log('📤 Sending animal selection request:', requestBody);

      const response = await fetch('http://127.0.0.1:5001/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Server error response:', errorText);
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('📥 Farm selection response:', data);

      // Remove loading message and add AI response
      setMessages(prev => {
        const filtered = prev.filter(msg => !msg.isLoading);
        return [...filtered, {
          id: Date.now().toString(),
          text: data.message,
          isUser: false,
          timestamp: new Date(),
          image: data.image,
          animalInfo: data.animalInfo,
          animalImages: data.animalImages,
          farmImages: data.farmImages,
          expenseImages: data.expenseImages,
          employeeSelection: data.needsEmployeeSelection ? {
            employees: data.employeeList || [],
            taskContext: data.context
          } : undefined,
          farmSelection: data.needsFarmSelection ? {
            farms: data.farmList || [],
            context: data.context
          } : undefined,
          context: data.context || undefined, // Preserve context
        }];
      });

      // Clear context after successful operations
      if (data.healthCheckSaved || data.saved || data.deleted) {
        setHealthCheckContext(null);
        console.log('✅ Operation completed in animal selection, context cleared');
      }
    } catch (error) {
      console.error('Error sending farm selection:', error);
      // Remove loading message and show error
      setMessages(prev => {
        const filtered = prev.filter(msg => !msg.isLoading);
        return [...filtered, {
          id: Date.now().toString(),
          text: 'Sorry, there was an error processing your selection. Please try again.',
          isUser: false,
          timestamp: new Date(),
        }];
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleExpenseSelection = async (expenseId: string, expenseName: string) => {
    // Find the context from the most recent message with expenseImages
    const lastMessageWithContext = messages
      .slice()
      .reverse()
      .find(msg => msg.expenseImages && msg.context);

    const contextToSend = lastMessageWithContext?.context || {};

    console.log('🔍 Found context for expense selection:', contextToSend);

    // Send the expense ID as a message to select the expense
    const userMessage: Message = {
      id: Date.now().toString(),
      text: `Selected: ${expenseName}`,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    // Add loading message
    const loadingMessage: Message = {
      id: (Date.now() + 0.5).toString(),
      text: '...',
      isUser: false,
      timestamp: new Date(),
      isLoading: true,
    };

    setMessages(prev => [...prev, loadingMessage]);

    try {
      const requestBody = {
        prompt: expenseId, // Send expense ID as prompt for compatibility
        selectedExpenseId: expenseId,
        userId: user?.id,
        language: language,
        context: contextToSend, // Use the preserved context instead of empty object
        farms: farms.map(farm => ({
          id: farm.id,
          name: farm.name,
          ownerId: farm.ownerId
        }))
      };

      console.log('📤 Sending expense selection request:', requestBody);

      const response = await fetch('http://127.0.0.1:5001/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Server error response:', errorText);
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('📥 Expense selection response:', data);

      // Remove loading message and add AI response
      setMessages(prev => {
        const filtered = prev.filter(msg => !msg.isLoading);
        return [...filtered, {
          id: Date.now().toString(),
          text: data.message,
          isUser: false,
          timestamp: new Date(),
          image: data.image,
          animalInfo: data.animalInfo,
          animalImages: data.animalImages,
          farmImages: data.farmImages,
          expenseImages: data.expenseImages,
          employeeSelection: data.needsEmployeeSelection ? {
            employees: data.employeeList || [],
            taskContext: data.context
          } : undefined,
          farmSelection: data.needsFarmSelection ? {
            farms: data.farmList || [],
            context: data.context
          } : undefined,
          context: data.context || undefined, // Preserve context
        }];
      });

      // Clear context after successful operations
      if (data.healthCheckSaved || data.saved || data.deleted) {
        setHealthCheckContext(null);
        console.log('✅ Operation completed in expense selection, context cleared');
      }

      setIsLoading(false);
    } catch (error) {
      console.error('Error selecting expense:', error);
      setMessages(prev => {
        const filtered = prev.filter(msg => !msg.isLoading);
        return [...filtered, {
          id: Date.now().toString(),
          text: 'Sorry, there was an error processing your expense selection. Please try again.',
          isUser: false,
          timestamp: new Date(),
        }];
      });
      setIsLoading(false);
    }
  };

  const handleFarmSelection = async (farmId: string, farmName: string, farmContext: any) => {
    // Send the farm ID as a message to select the farm
    const userMessage: Message = {
      id: Date.now().toString(),
      text: `Selected: ${farmName}`,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    // Add loading message
    const loadingMessage: Message = {
      id: (Date.now() + 0.5).toString(),
      text: '...',
      isUser: false,
      timestamp: new Date(),
      isLoading: true,
    };

    setMessages(prev => [...prev, loadingMessage]);

    try {
      // Prepare request body based on context
      const requestBody = {
        prompt: farmContext?.action === 'delete_farm' ? farmId : `Selected farm: ${farmName}`,
        selectedFarmId: farmId,
        userId: user?.id,
        language: language,
        context: farmContext,
        farms: farms.map(farm => ({
          id: farm.id,
          name: farm.name,
          ownerId: farm.ownerId
        }))
      };

      console.log('📤 Sending farm selection request:', requestBody);
      console.log('📤 Context being sent:', JSON.stringify(farmContext, null, 2));

      const response = await fetch('http://127.0.0.1:5001/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Server error response:', errorText);
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('📥 Farm selection response:', data);

      // Remove loading message and add AI response
      setMessages(prev => {
        const filtered = prev.filter(msg => !msg.isLoading);
        return [...filtered, {
          id: Date.now().toString(),
          text: data.message,
          isUser: false,
          timestamp: new Date(),
          image: data.image,
          animalInfo: data.animalInfo,
          animalImages: data.animalImages,
          farmImages: data.farmImages,
          expenseImages: data.expenseImages,
          employeeSelection: data.needsEmployeeSelection ? {
            employees: data.employeeList || [],
            taskContext: data.context
          } : undefined,
          farmSelection: data.needsFarmSelection ? {
            farms: data.farmList || [],
            context: data.context
          } : undefined,
          context: data.context || undefined, // Preserve context
        }];
      });

      // Clear context after successful operations
      if (data.healthCheckSaved || data.saved || data.deleted) {
        setHealthCheckContext(null);
        console.log('✅ Operation completed in farm selection, context cleared');
      }
    } catch (error) {
      console.error('Error sending animal selection:', error);
      // Remove loading message and show error
      setMessages(prev => {
        const filtered = prev.filter(msg => !msg.isLoading);
        return [...filtered, {
          id: Date.now().toString(),
          text: 'Sorry, there was an error processing your selection. Please try again.',
          isUser: false,
          timestamp: new Date(),
        }];
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Add this function after handleFarmSelection
  const handleModuleSelection = async (moduleId: string, moduleName: string, moduleContext: any) => {
    console.log('📋 Module selected:', moduleId, moduleName);
    setIsLoading(true);

    // Add user message showing selection
    const userMessage: Message = {
      id: Date.now().toString(),
      text: `Selected: ${moduleName}`,
      isUser: true,
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, userMessage]);

    try {
      const requestBody = {
        prompt: moduleId, // Send the module ID
        language: language,
        userId: user?.id,
        context: moduleContext,
        selectedModuleId: moduleId, // Explicitly send the selected module ID
        farms: farms.map(farm => ({
          id: farm.id,
          name: farm.name,
          ownerId: farm.ownerId
        }))
      };

      console.log('📤 Sending module selection request:', requestBody);

      const response = await fetch('http://127.0.0.1:5001/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Server error response:', errorText);
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('📥 Module selection response:', data);

      // Remove loading message and add AI response
      setMessages(prev => {
        const filtered = prev.filter(msg => !msg.isLoading);
        return [...filtered, {
          id: (Date.now() + 1).toString(),
          text: data.message,
          isUser: false,
          timestamp: new Date(),
          image: data.image,
          animalInfo: data.animalInfo,
          animalImages: data.animalImages,
          farmImages: data.farmImages,
          expenseImages: data.expenseImages,
          moduleImages: data.moduleImages,
          employeeSelection: data.needsEmployeeSelection ? {
            employees: data.employeeList || [],
            taskContext: data.context
          } : undefined,
          farmSelection: data.needsFarmSelection ? {
            farms: data.farmList || [],
            context: data.context
          } : undefined,
          context: data.context || undefined,
        }];
      });

    } catch (error) {
      console.error('Error sending module selection:', error);
      setMessages(prev => {
        const filtered = prev.filter(msg => !msg.isLoading);
        return [...filtered, {
          id: Date.now().toString(),
          text: 'Sorry, there was an error processing your selection. Please try again.',
          isUser: false,
          timestamp: new Date(),
        }];
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Add this component for loading animation
  const LoadingDots = () => {
    const [dots, setDots] = useState('');

    useEffect(() => {
      const interval = setInterval(() => {
        setDots(prev => {
          if (prev === '...') return '';
          return prev + '.';
        });
      }, 500);

      return () => clearInterval(interval);
    }, []);

    return (
      <View style={styles.loadingContainer}>
        <View style={styles.loadingBubble}>
          <Text style={styles.loadingText}>{dots || '.'}</Text>
        </View>
      </View>
    );
  };

  const renderMessage = ({ item }: { item: Message }) => {

    return (
      <View style={[
        styles.messageContainer,
        item.isUser ? styles.userMessage : styles.aiMessage
      ]}>
        {item.image && (
          <View style={styles.imageContainer}>
            <Image source={{ uri: item.image }} style={styles.messageImage} />
            {item.animalInfo && (
              <View style={styles.animalInfoOverlay}>
                <Text style={styles.animalNameText}>
                  🐄 {item.animalInfo.name} ({item.animalInfo.species})
                </Text>
              </View>
            )}
          </View>
        )}
        {item.animalImages && item.animalImages.length > 0 && (
          <View style={styles.animalImagesContainer}>
            {item.animalImages.map((animal, index) => (
              <TouchableOpacity
                key={`${animal.name}-${index}`}
                style={styles.animalImageRow}
                onPress={() => handleAnimalSelection(animal.id, animal.name)}
                activeOpacity={0.7}
              >
                <Image
                  source={{ uri: animal.imageUri }}
                  style={styles.animalImageInline}
                  resizeMode="cover"
                />
                <Text style={styles.animalImageText}>
                  {index + 1}. {animal.name} ({animal.species})
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        )}

        {/* Farm Selection - Similar to Animal Images */}
        {item.farmImages && item.farmImages.length > 0 && (
          <View style={styles.animalImagesContainer}>
            {item.farmImages.map((farm, index) => (
              <TouchableOpacity
                key={`${farm.name}-${index}`}
                style={styles.animalImageRow}
                onPress={() => handleFarmSelection(farm.id, farm.name, item.farmSelection?.context)}
                activeOpacity={0.7}
              >
                {farm.imageUri ? (
                  <Image
                    source={{ uri: farm.imageUri }}
                    style={styles.animalImageInline}
                    resizeMode="cover"
                  />
                ) : (
                  <View style={[styles.animalImageInline, styles.placeholderImage]}>
                    <Text style={styles.placeholderText}>🏡</Text>
                  </View>
                )}
                <Text style={styles.animalImageText}>
                  {index + 1}. {farm.name} ({farm.location})
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        )}

        {/* Expense Selection - Similar to Animal Images */}
        {item.expenseImages && item.expenseImages.length > 0 && (
          <View style={styles.animalImagesContainer}>
            {item.expenseImages.map((expense, index) => (
              <TouchableOpacity
                key={`${expense.id}-${index}`}
                style={styles.expenseCard}
                onPress={() => handleExpenseSelection(expense.id, expense.name)}
                activeOpacity={0.7}
              >
                {expense.imageUri ? (
                  <Image
                    source={{ uri: expense.imageUri }}
                    style={styles.expenseImage}
                    resizeMode="cover"
                  />
                ) : (
                  <View style={[styles.expenseImage, styles.placeholderImage]}>
                    <Text style={styles.placeholderText}>🧾</Text>
                  </View>
                )}
                <View style={styles.expenseInfo}>
                  <Text style={styles.expenseTitle}>{expense.name}</Text>
                  <Text style={styles.expenseAmount}>{expense.amount}</Text>
                  <Text style={styles.expenseDetails}>
                    {expense.paymentMethod} | {expense.date}
                  </Text>
                  {expense.description && (
                    <Text style={styles.expenseDescription}>{expense.description}</Text>
                  )}
                </View>
              </TouchableOpacity>
            ))}
          </View>
        )}

        {/* Employee Selection - Similar to Animal Images */}
        {item.employeeSelection && (
          <View style={styles.employeeSelectionContainer}>
            <Text style={styles.employeeSelectionTitle}>
              👥 Select an employee:
            </Text>
            {item.employeeSelection.employees.map((employee, index) => (
              <TouchableOpacity
                key={employee.id}
                style={styles.employeeItem}
                onPress={() => handleEmployeeSelection(employee.id, employee.name, item.employeeSelection?.taskContext)}
                activeOpacity={0.7}
              >
                {employee.imageUri ? (
                  <Image
                    source={{ uri: employee.imageUri }}
                    style={styles.employeeImage}
                    resizeMode="cover"
                  />
                ) : (
                  <View style={styles.employeeImagePlaceholder}>
                    <Text style={styles.employeeInitial}>
                      {employee.name.charAt(0).toUpperCase()}
                    </Text>
                  </View>
                )}
                <View style={styles.employeeInfo}>
                  <Text style={styles.employeeName}>
                    {index + 1}. {employee.name}
                  </Text>
                  <Text style={styles.employeeRole}>
                    {employee.role.charAt(0).toUpperCase() + employee.role.slice(1)}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        )}

        {/* Task Selection - Similar to Employee Selection */}
        {item.taskSelection && (
          <View style={styles.taskSelectionContainer}>
            <Text style={styles.taskSelectionTitle}>
              🗑️ Select a task to delete:
            </Text>
            {item.taskSelection.tasks.map((task, index) => (
              <TouchableOpacity
                key={task.id}
                style={styles.taskItem}
                onPress={() => handleTaskSelection(task.id, task.label, item.taskSelection?.context)}
                activeOpacity={0.7}
              >
                <View style={styles.taskIconContainer}>
                  <Text style={styles.taskIcon}>
                    {task.priority === 'high' ? '🔴' : task.priority === 'medium' ? '🟡' : '🟢'}
                  </Text>
                </View>
                <View style={styles.taskInfo}>
                  <Text style={styles.taskTitle}>
                    {index + 1}. {task.label}
                  </Text>
                  <Text style={styles.taskDescription}>
                    {task.description}
                  </Text>
                  {task.assigneeName && (
                    <Text style={styles.taskAssignee}>
                      👤 {task.assigneeName}
                    </Text>
                  )}
                  {task.dueDate && (
                    <Text style={styles.taskDueDate}>
                      📅 Due: {new Date(task.dueDate).toLocaleDateString()}
                    </Text>
                  )}
                </View>
                <View style={styles.taskStatusContainer}>
                  <Text style={styles.taskStatus}>
                    {task.status === 'pending' ? '⏳' : task.status === 'completed' ? '✅' : '❓'}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        )}
        {/* Module Selection - Similar to Farm Images */}
        {item.moduleImages && item.moduleImages.length > 0 && (
          <View style={styles.animalImagesContainer}>
            {item.moduleImages.map((module, index) => (
              <TouchableOpacity
                key={`${module.name}-${index}`}
                style={styles.animalImageRow}
                onPress={() => handleModuleSelection(module.id, module.name, item.context)}
                activeOpacity={0.7}
              >
                <View style={[styles.animalImageInline, styles.placeholderImage]}>
                  <Text style={styles.placeholderText}>{module.icon}</Text>
                </View>
                <Text style={styles.animalImageText}>
                  {index + 1}. {module.name} - {module.description}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        )}

        {item.isLoading ? (
          <LoadingDots />
        ) : item.text ? (
          <Text style={[
            styles.messageText,
            item.isUser ? styles.userMessageText : styles.aiMessageText,
            language === 'ur' ? styles.urduText : null
          ]}>
            {item.text}
          </Text>
        ) : null}
      </View>
    )
  };

  const styles = getStyles(themedColors, language);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={[styles.headerTitle, language === 'ur' ? styles.urduText : null]}>
          {(() => {
            const selectedFarm = getSelectedFarm();
            if (selectedFarm) {
              return language === 'ur'
                ? `${selectedFarm.name} میں کام کر رہے ہیں`
                : `Working in ${selectedFarm.name}`;
            } else if (farms && farms.length > 0) {
              return language === 'ur'
                ? `${farms[0].name} میں کام کر رہے ہیں`
                : `Working in ${farms[0].name}`;
            } else {
              return t('chat.title');
            }
          })()}
        </Text>
        {farms && farms.length > 1 && (
          <Text style={[styles.headerSubtitle, language === 'ur' ? styles.urduText : null]}>
            {language === 'ur'
              ? `${farms.length} فارمز دستیاب`
              : `${farms.length} farms available`
            }
          </Text>
        )}
      </View>

      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={item => item.id}
        style={styles.messagesList}
        onContentSizeChange={() => flatListRef.current?.scrollToEnd()}
      />

      {selectedImage && (
        <View style={styles.selectedImageContainer}>
          <Image source={{ uri: selectedImage }} style={styles.selectedImage} />
          <TouchableOpacity style={styles.removeImageButton} onPress={removeSelectedImage}>
            <X size={16} color="white" />
          </TouchableOpacity>
        </View>
      )}

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.inputContainer}
      >
        <View style={styles.inputRow}>
          <TouchableOpacity style={styles.attachButton} onPress={handleImagePicker}>
            <Paperclip size={20} color={themedColors.textSecondary} />
          </TouchableOpacity>

          <TextInput
            style={[
              styles.textInput,
              language === 'ur' ? styles.urduText : null
            ]}
            value={inputText}
            onChangeText={setInputText}
            placeholder={t('chat.inputPlaceholder')}
            placeholderTextColor={themedColors.textSecondary}
            multiline
            maxLength={1000}
          />

          <TouchableOpacity style={styles.voiceButton} onPress={handleVoicePress}>
            <Mic size={20} color="#999" />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.sendButton, (!inputText.trim() && !selectedImage || isLoading) && styles.sendButtonDisabled]}
            onPress={sendMessage}
            disabled={(!inputText.trim() && !selectedImage) || isLoading}
          >
            <Send size={20} color="white" />
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>


    </SafeAreaView>
  );
}

const getStyles = (themedColors: any, language: string) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: themedColors.background,
  },
  header: {
    padding: 16,
    backgroundColor: themedColors.surface,
    borderBottomWidth: 1,
    borderBottomColor: themedColors.border,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    color: themedColors.text,
  },
  headerSubtitle: {
    fontSize: 14,
    color: themedColors.textSecondary,
    textAlign: 'center',
    marginTop: 4,
  },
  urduText: {
    fontFamily: language === 'ur' ? 'NotoSansUrdu' : undefined,
    textAlign: language === 'ur' ? 'right' : 'left',
  },
  messagesList: {
    flex: 1,
    padding: 16,
  },
  messageContainer: {
    marginVertical: 4,
    padding: 12,
    borderRadius: 16,
    maxWidth: '80%',
  },
  userMessage: {
    backgroundColor: themedColors.primary,
    alignSelf: 'flex-end',
  },
  aiMessage: {
    backgroundColor: themedColors.surface,
    alignSelf: 'flex-start',
    borderWidth: 1,
    borderColor: themedColors.border,
  },
  messageText: {
    fontSize: 16,
  },
  userMessageText: {
    color: 'white',
  },
  aiMessageText: {
    color: themedColors.text,
  },
  messageImage: {
    width: 200,
    height: 150,
    borderRadius: 8,
    marginBottom: 8,
  },
  selectedImageContainer: {
    margin: 16,
    position: 'relative',
  },
  selectedImage: {
    width: 100,
    height: 75,
    borderRadius: 8,
  },
  removeImageButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: themedColors.error,
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  inputContainer: {
    backgroundColor: themedColors.surface,
    borderTopWidth: 1,
    borderTopColor: themedColors.border,
  },
  inputRow: {
    flexDirection: 'row',
    padding: 16,
    alignItems: 'flex-end',
    gap: 8,
  },
  attachButton: {
    padding: 8,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: themedColors.border,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    maxHeight: 100,
    color: themedColors.text,
    backgroundColor: themedColors.background,
  },
  voiceButton: {
    padding: 8,
  },
  sendButton: {
    backgroundColor: themedColors.primary,
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: themedColors.textSecondary,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 10,
  },
  // loadingBubble: {
  //   width: 40,
  //   height: 40,
  //   borderRadius: 20,
  //   backgroundColor: themedColors.primary,
  //   alignItems: 'center',
  //   justifyContent: 'center',
  // },
  loadingBubble: {
    backgroundColor: themedColors.primary,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  loadingText: {
    color: 'white',
    fontSize: 40,
  },
  imageContainer: {
    position: 'relative',
    marginBottom: 8,
  },
  animalInfoOverlay: {
    position: 'absolute',
    bottom: 8,
    left: 8,
    right: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  animalNameText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  animalImagesContainer: {
    marginVertical: 8,
    paddingHorizontal: 8,
  },
  animalImageRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    paddingVertical: 8,
    paddingHorizontal: 8,
    borderRadius: 8,
    backgroundColor: themedColors.surface,
    borderWidth: 1,
    borderColor: themedColors.border,
  },
  animalImageInline: {
    width: 40,
    height: 40,
    borderRadius: 6,
    marginRight: 8,
  },
  animalImageText: {
    fontSize: 14,
    color: themedColors.text,
    flex: 1,
  },
  placeholderImage: {
    backgroundColor: themedColors.border,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderText: {
    fontSize: 16,
    color: themedColors.text,
  },

  // Expense Selection Styles
  expenseCard: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingVertical: 12,
    paddingHorizontal: 12,
    borderRadius: 12,
    backgroundColor: themedColors.surface,
    borderWidth: 1,
    borderColor: themedColors.border,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  expenseImage: {
    width: 50,
    height: 50,
    borderRadius: 8,
    marginRight: 12,
  },
  expenseInfo: {
    flex: 1,
  },
  expenseTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: themedColors.text,
    marginBottom: 4,
  },
  expenseAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: themedColors.primary,
    marginBottom: 4,
  },
  expenseDetails: {
    fontSize: 12,
    color: themedColors.textSecondary,
    marginBottom: 2,
  },
  expenseDescription: {
    fontSize: 12,
    color: themedColors.textSecondary,
    fontStyle: 'italic',
  },

  // Employee Selection Inline Styles
  employeeSelectionContainer: {
    marginTop: 10,
    marginBottom: 10,
  },
  employeeSelectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: themedColors.text,
    marginBottom: 10,
  },
  employeeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    marginBottom: 8,
    backgroundColor: themedColors.surface,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: themedColors.border,
  },
  employeeImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  employeeImagePlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: themedColors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  employeeInitial: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  employeeInfo: {
    flex: 1,
  },
  employeeName: {
    fontSize: 16,
    fontWeight: '500',
    color: themedColors.text,
    marginBottom: 2,
  },
  employeeRole: {
    fontSize: 14,
    color: themedColors.textSecondary,
  },

  // Task Selection Inline Styles
  taskSelectionContainer: {
    marginTop: 10,
    marginBottom: 10,
  },
  taskSelectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: themedColors.text,
    marginBottom: 10,
  },
  taskItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    marginBottom: 8,
    backgroundColor: themedColors.surface,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: themedColors.border,
  },
  taskIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: themedColors.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    borderWidth: 1,
    borderColor: themedColors.border,
  },
  taskIcon: {
    fontSize: 16,
  },
  taskInfo: {
    flex: 1,
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: themedColors.text,
    marginBottom: 2,
  },
  taskDescription: {
    fontSize: 14,
    color: themedColors.textSecondary,
    marginBottom: 2,
  },
  taskAssignee: {
    fontSize: 12,
    color: themedColors.primary,
    marginBottom: 2,
  },
  taskDueDate: {
    fontSize: 12,
    color: themedColors.textSecondary,
  },
  taskStatusContainer: {
    width: 30,
    alignItems: 'center',
  },
  taskStatus: {
    fontSize: 16,
  },
});

export { ChatScreen };










