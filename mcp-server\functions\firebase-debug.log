[debug] [2025-07-25T11:59:36.162Z] ----------------------------------------------------------------------
[debug] [2025-07-25T11:59:36.174Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js emulators:start --only functions
[debug] [2025-07-25T11:59:36.175Z] CLI Version:   14.11.1
[debug] [2025-07-25T11:59:36.176Z] Platform:      win32
[debug] [2025-07-25T11:59:36.176Z] Node Version:  v22.17.0
[debug] [2025-07-25T11:59:36.177Z] Time:          Fri Jul 25 2025 16:59:36 GMT+0500 (Pakistan Standard Time)
[debug] [2025-07-25T11:59:36.178Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-25T11:59:37.390Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-25T11:59:37.392Z] > authorizing via signed-in user (<EMAIL>)
[info] i  emulators: Starting emulators: functions {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: functions"}}
[debug] [2025-07-25T11:59:37.436Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-25T11:59:37.437Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}]},"metadata":{"message":"assigned listening specs for emulators"}}
[debug] [2025-07-25T11:59:37.460Z] [hub] writing locator at C:\Users\<USER>\AppData\Local\Temp\hub-kissandost-9570f.json
[debug] [2025-07-25T11:59:37.533Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-25T11:59:37.534Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-25T11:59:37.534Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-25T11:59:37.535Z] late-assigned ports for functions and eventarc emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5001}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9299}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9499}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] !  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: apphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: \u001b[1mapphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect\u001b[22m"}}
[debug] [2025-07-25T11:59:37.609Z] defaultcredentials: writing to file C:\Users\<USER>\AppData\Roaming\firebase\malikeahtesham111_gmail_com_application_default_credentials.json
[debug] [2025-07-25T11:59:37.620Z] Setting GAC to C:\Users\<USER>\AppData\Roaming\firebase\malikeahtesham111_gmail_com_application_default_credentials.json {"metadata":{"emulator":{"name":"functions"},"message":"Setting GAC to C:\\Users\\<USER>\\AppData\\Roaming\\firebase\\malikeahtesham111_gmail_com_application_default_credentials.json"}}
[debug] [2025-07-25T11:59:37.625Z] Checked if tokens are valid: true, expires at: 1753447087123
[debug] [2025-07-25T11:59:37.625Z] Checked if tokens are valid: true, expires at: 1753447087123
[debug] [2025-07-25T11:59:37.634Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/kissandost-9570f/adminSdkConfig [none]
[debug] [2025-07-25T11:59:38.377Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/kissandost-9570f/adminSdkConfig 200
[debug] [2025-07-25T11:59:38.379Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/kissandost-9570f/adminSdkConfig {"projectId":"kissandost-9570f","databaseURL":"https://kissandost-9570f-default-rtdb.firebaseio.com","storageBucket":"kissandost-9570f.firebasestorage.app"}
[info] i  functions: Watching "D:\Sherry\projects\apps\mcp-server\functions" for Cloud Functions... {"metadata":{"emulator":{"name":"functions"},"message":"Watching \"D:\\Sherry\\projects\\apps\\mcp-server\\functions\" for Cloud Functions..."}}
[debug] [2025-07-25T11:59:38.609Z] Validating nodejs source
[debug] [2025-07-25T11:59:44.189Z] > [functions] package.json contents: {
  "name": "functions",
  "description": "Cloud Functions for Firebase",
  "scripts": {
    "serve": "firebase emulators:start --only functions",
    "shell": "firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "index.js",
  "dependencies": {
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1",
    "axios": "^1.10.0",
    "cors": "^2.8.5",
    "dotenv": "^17.2.0",
    "express": "^4.18.2",
    "multer": "^1.4.5-lts.1",
    "openai": "^4.20.1",
    "uuid": "^9.0.1"
  },
  "devDependencies": {
    "firebase-functions-test": "^3.1.0"
  },
  "private": true
}
[debug] [2025-07-25T11:59:44.190Z] Building nodejs source
[debug] [2025-07-25T11:59:44.191Z] Failed to find version of module node: reached end of search path D:\Sherry\projects\apps\mcp-server\functions\node_modules
[info] +  functions: Using node@22 from host. 
[info] i  functions: Loaded environment variables from .env. 
[debug] [2025-07-25T11:59:44.211Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-25T11:59:44.238Z] Found firebase-functions binary at 'D:\Sherry\projects\apps\mcp-server\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8554

[info] [dotenv@17.2.0] injecting env (2) from .env (tip: 🛠️  run anywhere with `dotenvx run -- yourcommand`)

[debug] [2025-07-25T11:59:52.899Z] Got response from /__/functions.yaml {"endpoints":{"animalApp":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","region":["australia-southeast1"],"labels":{},"httpsTrigger":{},"entryPoint":"animalApp"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] +  functions: Loaded functions definitions from source: animalApp. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: animalApp."}}
[info] +  functions[australia-southeast1-animalApp]: http function initialized (http://127.0.0.1:5001/kissandost-9570f/australia-southeast1/animalApp). {"metadata":{"emulator":{"name":"functions"},"message":"\u001b[1mhttp\u001b[22m function initialized (http://127.0.0.1:5001/kissandost-9570f/australia-southeast1/animalApp)."}}
[debug] [2025-07-25T11:59:57.185Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: . If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
[info] 
┌─────────────────────────────────────────────────────────────┐
│ ✔  All emulators ready! It is now safe to connect your app. │
│ i  View Emulator UI at http://127.0.0.1:4000/               │
└─────────────────────────────────────────────────────────────┘

┌───────────┬────────────────┬─────────────────────────────────┐
│ Emulator  │ Host:Port      │ View in Emulator UI             │
├───────────┼────────────────┼─────────────────────────────────┤
│ Functions │ 127.0.0.1:5001 │ http://127.0.0.1:4000/functions │
└───────────┴────────────────┴─────────────────────────────────┘
  Emulator Hub host: 127.0.0.1 port: 4400
  Other reserved ports: 4500

Issues? Report them at https://github.com/firebase/firebase-tools/issues and attach the *-debug.log files.
 
[debug] [2025-07-25T12:01:03.058Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:01:03.057Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:01:03.061Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:01:03.057Z"],"workRunningCount":1}
[debug] [2025-07-25T12:01:03.063Z] Accepted request OPTIONS /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-25T12:01:03.078Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-25T12:01:03.080Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[info] i  functions: Loaded environment variables from .env. 
[debug] [2025-07-25T12:01:03.268Z] [worker-pool] addWorker(australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] addWorker(australia-southeast1-animalApp)"}}
[debug] [2025-07-25T12:01:03.275Z] [worker-pool] Adding worker with key australia-southeast1-animalApp, total=1 {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] Adding worker with key australia-southeast1-animalApp, total=1"}}
[debug] [2025-07-25T12:01:09.853Z] [runtime-status] [31968] Resolved module firebase-admin {"declared":true,"installed":true,"version":"12.7.0","resolution":"D:\\Sherry\\projects\\apps\\mcp-server\\functions\\node_modules\\firebase-admin\\lib\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [31968] Resolved module firebase-admin {\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"D:\\\\Sherry\\\\projects\\\\apps\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-admin\\\\lib\\\\index.js\"}"}}
[debug] [2025-07-25T12:01:09.867Z] [runtime-status] [31968] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.4.0","resolution":"D:\\Sherry\\projects\\apps\\mcp-server\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [31968] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.4.0\",\"resolution\":\"D:\\\\Sherry\\\\projects\\\\apps\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-07-25T12:01:09.872Z] [runtime-status] [31968] Outgoing network have been stubbed. [{"name":"http","status":"mocked"},{"name":"http","status":"mocked"},{"name":"https","status":"mocked"},{"name":"https","status":"mocked"},{"name":"net","status":"mocked"}] {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [31968] Outgoing network have been stubbed. [{\"name\":\"http\",\"status\":\"mocked\"},{\"name\":\"http\",\"status\":\"mocked\"},{\"name\":\"https\",\"status\":\"mocked\"},{\"name\":\"https\",\"status\":\"mocked\"},{\"name\":\"net\",\"status\":\"mocked\"}]"}}
[debug] [2025-07-25T12:01:09.879Z] [runtime-status] [31968] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.4.0","resolution":"D:\\Sherry\\projects\\apps\\mcp-server\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [31968] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.4.0\",\"resolution\":\"D:\\\\Sherry\\\\projects\\\\apps\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-07-25T12:01:17.195Z] [runtime-status] [31968] Checked functions.config() {"config":{}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [31968] Checked functions.config() {\"config\":{}}"}}
[debug] [2025-07-25T12:01:17.196Z] [runtime-status] [31968] firebase-functions has been stubbed. {"functionsResolution":{"declared":true,"installed":true,"version":"6.4.0","resolution":"D:\\Sherry\\projects\\apps\\mcp-server\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [31968] firebase-functions has been stubbed. {\"functionsResolution\":{\"declared\":true,\"installed\":true,\"version\":\"6.4.0\",\"resolution\":\"D:\\\\Sherry\\\\projects\\\\apps\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}}"}}
[debug] [2025-07-25T12:01:17.197Z] [runtime-status] [31968] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.4.0","resolution":"D:\\Sherry\\projects\\apps\\mcp-server\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [31968] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.4.0\",\"resolution\":\"D:\\\\Sherry\\\\projects\\\\apps\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-07-25T12:01:17.349Z] [runtime-status] [31968] Resolved module firebase-admin {"declared":true,"installed":true,"version":"12.7.0","resolution":"D:\\Sherry\\projects\\apps\\mcp-server\\functions\\node_modules\\firebase-admin\\lib\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [31968] Resolved module firebase-admin {\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"D:\\\\Sherry\\\\projects\\\\apps\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-admin\\\\lib\\\\index.js\"}"}}
[debug] [2025-07-25T12:01:17.373Z] [runtime-status] [31968] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.4.0","resolution":"D:\\Sherry\\projects\\apps\\mcp-server\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [31968] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.4.0\",\"resolution\":\"D:\\\\Sherry\\\\projects\\\\apps\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-07-25T12:01:17.374Z] [runtime-status] [31968] firebase-admin has been stubbed. {"adminResolution":{"declared":true,"installed":true,"version":"12.7.0","resolution":"D:\\Sherry\\projects\\apps\\mcp-server\\functions\\node_modules\\firebase-admin\\lib\\index.js"}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [31968] firebase-admin has been stubbed. {\"adminResolution\":{\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"D:\\\\Sherry\\\\projects\\\\apps\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-admin\\\\lib\\\\index.js\"}}"}}
[info] >  [dotenv@17.2.0] injecting env (0) from .env (tip: 🔐 prevent committing .env to code: https://dotenvx.com/precommit) {"user":"[dotenv@17.2.0] injecting env (0) from .env (tip: 🔐 prevent committing .env to code: https://dotenvx.com/precommit)","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m [dotenv@17.2.0] injecting env (0) from .env (tip: 🔐 prevent committing .env to code: https://dotenvx.com/precommit)"}}
*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
[debug] [2025-07-25T12:01:18.290Z] [runtime-status] [31968] Functions runtime initialized. {"cwd":"D:\\Sherry\\projects\\apps\\mcp-server\\functions","node_version":"22.17.0"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [31968] Functions runtime initialized. {\"cwd\":\"D:\\\\Sherry\\\\projects\\\\apps\\\\mcp-server\\\\functions\",\"node_version\":\"22.17.0\"}"}}
[debug] [2025-07-25T12:01:18.291Z] [runtime-status] [31968] Listening to port: \\?\pipe\fire_emu_d751d97dee8556a1 {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [31968] Listening to port: \\\\?\\pipe\\fire_emu_d751d97dee8556a1"}}
[debug] [2025-07-25T12:01:18.422Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE"}}
[debug] [2025-07-25T12:01:18.423Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-25T12:01:18.426Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY"}}
[debug] [2025-07-25T12:01:18.461Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 35.0509ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 35.0509ms"}}
[debug] [2025-07-25T12:01:18.476Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE"}}
[debug] [2025-07-25T12:01:18.476Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-25T12:01:18.477Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-25T12:01:18.477Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:01:18.513Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:01:18.513Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:01:18.514Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:01:18.513Z"],"workRunningCount":1}
[debug] [2025-07-25T12:01:18.514Z] Accepted request POST /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-25T12:01:18.522Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-25T12:01:18.523Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-25T12:01:18.523Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-25T12:01:18.532Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY"}}
[info] >  🔁 Initializing routes... {"user":"🔁 Initializing routes...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔁 Initializing routes..."}}
[info] >  OpenAI client initialized successfully {"user":"OpenAI client initialized successfully","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m OpenAI client initialized successfully"}}
[warn] !  functions: The Cloud Firestore emulator is not running, so calls to Firestore will affect production. {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"The Cloud Firestore emulator is not running, so calls to Firestore will affect production."}}
[warn] !  functions: The Firebase Storage emulator is not running, so calls to Firebase Storage will affect production. {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"The Firebase Storage emulator is not running, so calls to Firebase Storage will affect production."}}
[info] >  🔁 Routes Initialized Successfully... {"user":"🔁 Routes Initialized Successfully...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔁 Routes Initialized Successfully..."}}
[info] >  ............................ { {"user":"............................ {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ............................ {"}}
[info] >    id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    email: '<EMAIL>', {"user":"  email: '<EMAIL>',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   email: '<EMAIL>',"}}
[info] >    role: 'owner', {"user":"  role: 'owner',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   role: 'owner',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    preferAudio: false, {"user":"  preferAudio: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   preferAudio: false,"}}
[info] >    offlineMode: false, {"user":"  offlineMode: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   offlineMode: false,"}}
[info] >    emailVerified: true, {"user":"  emailVerified: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   emailVerified: true,"}}
[info] >    createdAt: 1746969090959, {"user":"  createdAt: 1746969090959,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   createdAt: 1746969090959,"}}
[info] >    name: 'Dr. Javad Ahmed', {"user":"  name: 'Dr. Javad Ahmed',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   name: 'Dr. Javad Ahmed',"}}
[info] >    ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ], {"user":"  ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],"}}
[info] >    profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90', {"user":"  profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',"}}
[info] >    lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 }, {"user":"  lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },"}}
[info] >    updatedAt: 1753292227987, {"user":"  updatedAt: 1753292227987,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   updatedAt: 1753292227987,"}}
[info] >    assignedFarmIds: [ {"user":"  assignedFarmIds: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   assignedFarmIds: ["}}
[info] >      'ell4xZlOUxIwbSTdk4gp', {"user":"    'ell4xZlOUxIwbSTdk4gp',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'ell4xZlOUxIwbSTdk4gp',"}}
[info] >      'YrwVdr1vr3KGqtXP3bqX', {"user":"    'YrwVdr1vr3KGqtXP3bqX',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'YrwVdr1vr3KGqtXP3bqX',"}}
[info] >      '7owH6eTfGSYqaRpgIsQE', {"user":"    '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7owH6eTfGSYqaRpgIsQE',"}}
[info] >      'icSTOLwTaC6WYgWjsgDk', {"user":"    'icSTOLwTaC6WYgWjsgDk',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'icSTOLwTaC6WYgWjsgDk',"}}
[info] >      'BtniJTurDAn0saHHbEGF', {"user":"    'BtniJTurDAn0saHHbEGF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'BtniJTurDAn0saHHbEGF',"}}
[info] >      'wFSF3LbHQwPYy6A0pu94', {"user":"    'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'wFSF3LbHQwPYy6A0pu94',"}}
[info] >      'fNgkVoyHni01zmozumbK', {"user":"    'fNgkVoyHni01zmozumbK',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'fNgkVoyHni01zmozumbK',"}}
[info] >      'C6FceATDCJxVtji42Q3D', {"user":"    'C6FceATDCJxVtji42Q3D',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'C6FceATDCJxVtji42Q3D',"}}
[info] >      'M5Pahb6Vi1rdq7KZB5LF', {"user":"    'M5Pahb6Vi1rdq7KZB5LF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'M5Pahb6Vi1rdq7KZB5LF',"}}
[info] >      'oXnGAP4V78vilhIVpIQN', {"user":"    'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'oXnGAP4V78vilhIVpIQN',"}}
[info] >      'NpS3CdrpHvPZClMMDeFc', {"user":"    'NpS3CdrpHvPZClMMDeFc',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'NpS3CdrpHvPZClMMDeFc',"}}
[info] >      'TlS4g9PWKN1wNBtjHEoq', {"user":"    'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >      'rb2n32HuXJlFOk44cAkJ', {"user":"    'rb2n32HuXJlFOk44cAkJ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'rb2n32HuXJlFOk44cAkJ',"}}
[info] >      'UWkNKuXggt70Oe4bkzcx', {"user":"    'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'UWkNKuXggt70Oe4bkzcx',"}}
[info] >      'JRAQxnj2u4vmHNuPHz3W' {"user":"    'JRAQxnj2u4vmHNuPHz3W'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'JRAQxnj2u4vmHNuPHz3W'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[debug] [2025-07-25T12:01:33.992Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 15460.8271ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 15460.8271ms"}}
[debug] [2025-07-25T12:01:33.996Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE"}}
[debug] [2025-07-25T12:01:33.997Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-25T12:01:33.997Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-25T12:01:34.002Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[info] >  ✏️ GENERAL EDIT PATTERN MATCHED! {"user":"✏️ GENERAL EDIT PATTERN MATCHED!","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ✏️ GENERAL EDIT PATTERN MATCHED!"}}
[info] >  requestType edit_general {"user":"requestType edit_general","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m requestType edit_general"}}
[info] >  📨 Context received: null {"user":"📨 Context received: null","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context received: null"}}
[info] >  📨 Context action: undefined {"user":"📨 Context action: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: undefined"}}
[info] >  📨 Context needsAnimalSelection: undefined {"user":"📨 Context needsAnimalSelection: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context needsAnimalSelection: undefined"}}
[info] >  Processing general edit request - showing module selection... {"user":"Processing general edit request - showing module selection...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Processing general edit request - showing module selection..."}}
[debug] [2025-07-25T12:02:22.135Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:02:22.135Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:02:22.136Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:02:22.135Z"],"workRunningCount":1}
[debug] [2025-07-25T12:02:22.137Z] Accepted request OPTIONS /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-25T12:02:22.155Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-25T12:02:22.156Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-25T12:02:22.157Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-25T12:02:22.171Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY"}}
[debug] [2025-07-25T12:02:22.186Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 15.6133ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 15.6133ms"}}
[debug] [2025-07-25T12:02:22.201Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE"}}
[debug] [2025-07-25T12:02:22.201Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-25T12:02:22.202Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-25T12:02:22.202Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:02:22.248Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:02:22.247Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:02:22.249Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:02:22.247Z"],"workRunningCount":1}
[debug] [2025-07-25T12:02:22.249Z] Accepted request POST /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-25T12:02:22.259Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-25T12:02:22.261Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-25T12:02:22.262Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-25T12:02:22.271Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY"}}
[info] >  ............................ { {"user":"............................ {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ............................ {"}}
[info] >    id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    email: '<EMAIL>', {"user":"  email: '<EMAIL>',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   email: '<EMAIL>',"}}
[info] >    role: 'owner', {"user":"  role: 'owner',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   role: 'owner',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    preferAudio: false, {"user":"  preferAudio: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   preferAudio: false,"}}
[info] >    offlineMode: false, {"user":"  offlineMode: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   offlineMode: false,"}}
[info] >    emailVerified: true, {"user":"  emailVerified: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   emailVerified: true,"}}
[info] >    createdAt: 1746969090959, {"user":"  createdAt: 1746969090959,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   createdAt: 1746969090959,"}}
[info] >    name: 'Dr. Javad Ahmed', {"user":"  name: 'Dr. Javad Ahmed',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   name: 'Dr. Javad Ahmed',"}}
[info] >    ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ], {"user":"  ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],"}}
[info] >    profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90', {"user":"  profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',"}}
[info] >    lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 }, {"user":"  lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },"}}
[info] >    updatedAt: 1753292227987, {"user":"  updatedAt: 1753292227987,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   updatedAt: 1753292227987,"}}
[info] >    assignedFarmIds: [ {"user":"  assignedFarmIds: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   assignedFarmIds: ["}}
[info] >      'ell4xZlOUxIwbSTdk4gp', {"user":"    'ell4xZlOUxIwbSTdk4gp',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'ell4xZlOUxIwbSTdk4gp',"}}
[info] >      'YrwVdr1vr3KGqtXP3bqX', {"user":"    'YrwVdr1vr3KGqtXP3bqX',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'YrwVdr1vr3KGqtXP3bqX',"}}
[info] >      '7owH6eTfGSYqaRpgIsQE', {"user":"    '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7owH6eTfGSYqaRpgIsQE',"}}
[info] >      'icSTOLwTaC6WYgWjsgDk', {"user":"    'icSTOLwTaC6WYgWjsgDk',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'icSTOLwTaC6WYgWjsgDk',"}}
[info] >      'BtniJTurDAn0saHHbEGF', {"user":"    'BtniJTurDAn0saHHbEGF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'BtniJTurDAn0saHHbEGF',"}}
[info] >      'wFSF3LbHQwPYy6A0pu94', {"user":"    'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'wFSF3LbHQwPYy6A0pu94',"}}
[info] >      'fNgkVoyHni01zmozumbK', {"user":"    'fNgkVoyHni01zmozumbK',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'fNgkVoyHni01zmozumbK',"}}
[info] >      'C6FceATDCJxVtji42Q3D', {"user":"    'C6FceATDCJxVtji42Q3D',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'C6FceATDCJxVtji42Q3D',"}}
[info] >      'M5Pahb6Vi1rdq7KZB5LF', {"user":"    'M5Pahb6Vi1rdq7KZB5LF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'M5Pahb6Vi1rdq7KZB5LF',"}}
[info] >      'oXnGAP4V78vilhIVpIQN', {"user":"    'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'oXnGAP4V78vilhIVpIQN',"}}
[info] >      'NpS3CdrpHvPZClMMDeFc', {"user":"    'NpS3CdrpHvPZClMMDeFc',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'NpS3CdrpHvPZClMMDeFc',"}}
[info] >      'TlS4g9PWKN1wNBtjHEoq', {"user":"    'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >      'rb2n32HuXJlFOk44cAkJ', {"user":"    'rb2n32HuXJlFOk44cAkJ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'rb2n32HuXJlFOk44cAkJ',"}}
[info] >      'UWkNKuXggt70Oe4bkzcx', {"user":"    'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'UWkNKuXggt70Oe4bkzcx',"}}
[info] >      'JRAQxnj2u4vmHNuPHz3W' {"user":"    'JRAQxnj2u4vmHNuPHz3W'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'JRAQxnj2u4vmHNuPHz3W'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[warn] !  External network resource requested!
   - URL: "https://api.openai.com/v1/chat/completions"
 - Be careful, this may be a production service. {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"External network resource requested!\n   - URL: \"https://api.openai.com/v1/chat/completions\"\n - Be careful, this may be a production service."}}
[info] >  ✏️ FARM EDIT PATTERN MATCHED! {"user":"✏️ FARM EDIT PATTERN MATCHED!","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ✏️ FARM EDIT PATTERN MATCHED!"}}
[info] >  requestType edit_farm {"user":"requestType edit_farm","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m requestType edit_farm"}}
[info] >  Processing farm edit request... {"user":"Processing farm edit request...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Processing farm edit request..."}}
[info] >  Farm edit analysis: { {"user":"Farm edit analysis: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Farm edit analysis: {"}}
[info] >    isEditRequest: true, {"user":"  isEditRequest: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   isEditRequest: true,"}}
[info] >    action: 'edit_farm', {"user":"  action: 'edit_farm',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   action: 'edit_farm',"}}
[info] >    farmName: null, {"user":"  farmName: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farmName: null,"}}
[info] >    fieldToUpdate: null, {"user":"  fieldToUpdate: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   fieldToUpdate: null,"}}
[info] >    newValue: null, {"user":"  newValue: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   newValue: null,"}}
[info] >    confidence: 70, {"user":"  confidence: 70,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   confidence: 70,"}}
[info] >    language: 'en' {"user":"  language: 'en'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en'"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[debug] [2025-07-25T12:02:27.462Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 5192.0739ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 5192.0739ms"}}
[debug] [2025-07-25T12:02:27.487Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE"}}
[debug] [2025-07-25T12:02:27.488Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:02:27.489Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[debug] [2025-07-25T12:02:27.490Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[debug] [2025-07-25T12:02:27.490Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[debug] [2025-07-25T12:02:27.492Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[debug] [2025-07-25T12:02:27.494Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[debug] [2025-07-25T12:02:27.495Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[debug] [2025-07-25T12:02:27.496Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[debug] [2025-07-25T12:02:27.497Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-25T12:02:27.497Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-25T12:02:46.347Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:02:46.347Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:02:46.347Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:02:46.347Z"],"workRunningCount":1}
[debug] [2025-07-25T12:02:46.348Z] Accepted request OPTIONS /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-25T12:02:46.368Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-25T12:02:46.368Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-25T12:02:46.369Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-25T12:02:46.387Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY"}}
[debug] [2025-07-25T12:02:46.402Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 16.3592ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 16.3592ms"}}
[debug] [2025-07-25T12:02:46.467Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE"}}
[debug] [2025-07-25T12:02:46.467Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-25T12:02:46.467Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-25T12:02:46.468Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:02:46.597Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:02:46.597Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:02:46.598Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:02:46.597Z"],"workRunningCount":1}
[debug] [2025-07-25T12:02:46.598Z] Accepted request POST /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-25T12:02:46.612Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-25T12:02:46.613Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-25T12:02:46.614Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-25T12:02:46.616Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY"}}
[info] >  📨 Context action: edit_farm {"user":"📨 Context action: edit_farm","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: edit_farm"}}
[info] >  📨 Context action: { {"user":"📨 Context action: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: {"}}
[info] >    prompt: 'Selected farm: Farm_003', {"user":"  prompt: 'Selected farm: Farm_003',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Selected farm: Farm_003',"}}
[info] >    selectedFarmId: 'JRAQxnj2u4vmHNuPHz3W', {"user":"  selectedFarmId: 'JRAQxnj2u4vmHNuPHz3W',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   selectedFarmId: 'JRAQxnj2u4vmHNuPHz3W',"}}
[info] >    userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    context: { {"user":"  context: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   context: {"}}
[info] >      action: 'edit_farm', {"user":"    action: 'edit_farm',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     action: 'edit_farm',"}}
[info] >      needsFarmSelection: true, {"user":"    needsFarmSelection: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     needsFarmSelection: true,"}}
[info] >      availableFarms: [ {"user":"    availableFarms: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     availableFarms: ["}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object] {"user":"      [Object], [Object]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object]"}}
[info] >      ] {"user":"    ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     ]"}}
[info] >    }, {"user":"  },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   },"}}
[info] >    farms: [ {"user":"  farms: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farms: ["}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: '7owH6eTfGSYqaRpgIsQE', {"user":"      id: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >        name: 'Haven View.', {"user":"      name: 'Haven View.',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Haven View.',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'BtniJTurDAn0saHHbEGF', {"user":"      id: 'BtniJTurDAn0saHHbEGF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'BtniJTurDAn0saHHbEGF',"}}
[info] >        name: 'Green Land..', {"user":"      name: 'Green Land..',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Green Land..',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'wFSF3LbHQwPYy6A0pu94', {"user":"      id: 'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'wFSF3LbHQwPYy6A0pu94',"}}
[info] >        name: 'Dairy House ', {"user":"      name: 'Dairy House ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Dairy House ',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'fNgkVoyHni01zmozumbK', {"user":"      id: 'fNgkVoyHni01zmozumbK',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'fNgkVoyHni01zmozumbK',"}}
[info] >        name: 'Octans Farm', {"user":"      name: 'Octans Farm',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Octans Farm',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'oXnGAP4V78vilhIVpIQN', {"user":"      id: 'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'oXnGAP4V78vilhIVpIQN',"}}
[info] >        name: 'Trade Farm', {"user":"      name: 'Trade Farm',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Trade Farm',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'NpS3CdrpHvPZClMMDeFc', {"user":"      id: 'NpS3CdrpHvPZClMMDeFc',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'NpS3CdrpHvPZClMMDeFc',"}}
[info] >        name: 'River view ', {"user":"      name: 'River view ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'River view ',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'TlS4g9PWKN1wNBtjHEoq', {"user":"      id: 'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >        name: 'Sabz Chara', {"user":"      name: 'Sabz Chara',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Sabz Chara',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'rb2n32HuXJlFOk44cAkJ', {"user":"      id: 'rb2n32HuXJlFOk44cAkJ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'rb2n32HuXJlFOk44cAkJ',"}}
[info] >        name: 'Farm_002', {"user":"      name: 'Farm_002',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Farm_002',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'UWkNKuXggt70Oe4bkzcx', {"user":"      id: 'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'UWkNKuXggt70Oe4bkzcx',"}}
[info] >        name: 'Farm_003', {"user":"      name: 'Farm_003',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Farm_003',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'JRAQxnj2u4vmHNuPHz3W', {"user":"      id: 'JRAQxnj2u4vmHNuPHz3W',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'JRAQxnj2u4vmHNuPHz3W',"}}
[info] >        name: 'Farm_003', {"user":"      name: 'Farm_003',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Farm_003',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      } {"user":"    }","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     }"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  ............................ { {"user":"............................ {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ............................ {"}}
[info] >    id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    email: '<EMAIL>', {"user":"  email: '<EMAIL>',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   email: '<EMAIL>',"}}
[info] >    role: 'owner', {"user":"  role: 'owner',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   role: 'owner',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    preferAudio: false, {"user":"  preferAudio: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   preferAudio: false,"}}
[info] >    offlineMode: false, {"user":"  offlineMode: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   offlineMode: false,"}}
[info] >    emailVerified: true, {"user":"  emailVerified: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   emailVerified: true,"}}
[info] >    createdAt: 1746969090959, {"user":"  createdAt: 1746969090959,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   createdAt: 1746969090959,"}}
[info] >    name: 'Dr. Javad Ahmed', {"user":"  name: 'Dr. Javad Ahmed',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   name: 'Dr. Javad Ahmed',"}}
[info] >    ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ], {"user":"  ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],"}}
[info] >    profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90', {"user":"  profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',"}}
[info] >    lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 }, {"user":"  lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },"}}
[info] >    updatedAt: 1753292227987, {"user":"  updatedAt: 1753292227987,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   updatedAt: 1753292227987,"}}
[info] >    assignedFarmIds: [ {"user":"  assignedFarmIds: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   assignedFarmIds: ["}}
[info] >      'ell4xZlOUxIwbSTdk4gp', {"user":"    'ell4xZlOUxIwbSTdk4gp',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'ell4xZlOUxIwbSTdk4gp',"}}
[info] >      'YrwVdr1vr3KGqtXP3bqX', {"user":"    'YrwVdr1vr3KGqtXP3bqX',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'YrwVdr1vr3KGqtXP3bqX',"}}
[info] >      '7owH6eTfGSYqaRpgIsQE', {"user":"    '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7owH6eTfGSYqaRpgIsQE',"}}
[info] >      'icSTOLwTaC6WYgWjsgDk', {"user":"    'icSTOLwTaC6WYgWjsgDk',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'icSTOLwTaC6WYgWjsgDk',"}}
[info] >      'BtniJTurDAn0saHHbEGF', {"user":"    'BtniJTurDAn0saHHbEGF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'BtniJTurDAn0saHHbEGF',"}}
[info] >      'wFSF3LbHQwPYy6A0pu94', {"user":"    'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'wFSF3LbHQwPYy6A0pu94',"}}
[info] >      'fNgkVoyHni01zmozumbK', {"user":"    'fNgkVoyHni01zmozumbK',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'fNgkVoyHni01zmozumbK',"}}
[info] >      'C6FceATDCJxVtji42Q3D', {"user":"    'C6FceATDCJxVtji42Q3D',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'C6FceATDCJxVtji42Q3D',"}}
[info] >      'M5Pahb6Vi1rdq7KZB5LF', {"user":"    'M5Pahb6Vi1rdq7KZB5LF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'M5Pahb6Vi1rdq7KZB5LF',"}}
[info] >      'oXnGAP4V78vilhIVpIQN', {"user":"    'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'oXnGAP4V78vilhIVpIQN',"}}
[info] >      'NpS3CdrpHvPZClMMDeFc', {"user":"    'NpS3CdrpHvPZClMMDeFc',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'NpS3CdrpHvPZClMMDeFc',"}}
[info] >      'TlS4g9PWKN1wNBtjHEoq', {"user":"    'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >      'rb2n32HuXJlFOk44cAkJ', {"user":"    'rb2n32HuXJlFOk44cAkJ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'rb2n32HuXJlFOk44cAkJ',"}}
[info] >      'UWkNKuXggt70Oe4bkzcx', {"user":"    'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'UWkNKuXggt70Oe4bkzcx',"}}
[info] >      'JRAQxnj2u4vmHNuPHz3W' {"user":"    'JRAQxnj2u4vmHNuPHz3W'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'JRAQxnj2u4vmHNuPHz3W'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  Processing farm selection for editing... {"user":"Processing farm selection for editing...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Processing farm selection for editing..."}}
[info] >  Selected farm ID for editing: JRAQxnj2u4vmHNuPHz3W {"user":"Selected farm ID for editing: JRAQxnj2u4vmHNuPHz3W","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Selected farm ID for editing: JRAQxnj2u4vmHNuPHz3W"}}
[debug] [2025-07-25T12:02:47.347Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 731.6897ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 731.6897ms"}}
[debug] [2025-07-25T12:02:47.353Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE"}}
[debug] [2025-07-25T12:02:47.353Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-25T12:02:47.354Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-25T12:02:47.354Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:03:25.320Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:03:25.320Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:03:25.321Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:03:25.320Z"],"workRunningCount":1}
[debug] [2025-07-25T12:03:25.321Z] Accepted request OPTIONS /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-25T12:03:25.328Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-25T12:03:25.328Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-25T12:03:25.329Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-25T12:03:25.332Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY"}}
[debug] [2025-07-25T12:03:25.341Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 9.1947ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 9.1947ms"}}
[debug] [2025-07-25T12:03:25.374Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE"}}
[debug] [2025-07-25T12:03:25.375Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-25T12:03:25.375Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-25T12:03:25.376Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:03:25.480Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:03:25.479Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:03:25.480Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:03:25.479Z"],"workRunningCount":1}
[debug] [2025-07-25T12:03:25.480Z] Accepted request POST /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-25T12:03:25.490Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-25T12:03:25.491Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-25T12:03:25.492Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-25T12:03:25.494Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY"}}
[info] >  📨 Context action: edit_farm {"user":"📨 Context action: edit_farm","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: edit_farm"}}
[info] >  📨 Context action: { {"user":"📨 Context action: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: {"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    farms: [ {"user":"  farms: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farms: ["}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: '7owH6eTfGSYqaRpgIsQE', {"user":"      id: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >        name: 'Haven View.', {"user":"      name: 'Haven View.',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Haven View.',"}}
[info] >        location: [Object] {"user":"      location: [Object]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       location: [Object]"}}
[info] >      } {"user":"    }","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     }"}}
[info] >    ], {"user":"  ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ],"}}
[info] >    previousAnimalData: null, {"user":"  previousAnimalData: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   previousAnimalData: null,"}}
[info] >    previousFarmData: null, {"user":"  previousFarmData: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   previousFarmData: null,"}}
[info] >    defaultFarmTypeId: 'B2bye8PZBQYqscxXoVqZ', {"user":"  defaultFarmTypeId: 'B2bye8PZBQYqscxXoVqZ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   defaultFarmTypeId: 'B2bye8PZBQYqscxXoVqZ',"}}
[info] >    defaultStatusId: 'tEbttSFNlpr6gGm5y66l', {"user":"  defaultStatusId: 'tEbttSFNlpr6gGm5y66l',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   defaultStatusId: 'tEbttSFNlpr6gGm5y66l',"}}
[info] >    defaultSizeUnitId: 'ezVe7GdWq6SAxwaREyIw', {"user":"  defaultSizeUnitId: 'ezVe7GdWq6SAxwaREyIw',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   defaultSizeUnitId: 'ezVe7GdWq6SAxwaREyIw',"}}
[info] >    farmLocation: '', {"user":"  farmLocation: '',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farmLocation: '',"}}
[info] >    context: { {"user":"  context: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   context: {"}}
[info] >      action: 'edit_farm', {"user":"    action: 'edit_farm',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     action: 'edit_farm',"}}
[info] >      needsFarmSelection: true, {"user":"    needsFarmSelection: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     needsFarmSelection: true,"}}
[info] >      availableFarms: [ {"user":"    availableFarms: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     availableFarms: ["}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object] {"user":"      [Object], [Object]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object]"}}
[info] >      ] {"user":"    ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     ]"}}
[info] >    }, {"user":"  },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   },"}}
[info] >    prompt: 'Change Location to Dina' {"user":"  prompt: 'Change Location to Dina'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Change Location to Dina'"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  ............................ { {"user":"............................ {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ............................ {"}}
[info] >    id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    email: '<EMAIL>', {"user":"  email: '<EMAIL>',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   email: '<EMAIL>',"}}
[info] >    role: 'owner', {"user":"  role: 'owner',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   role: 'owner',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    preferAudio: false, {"user":"  preferAudio: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   preferAudio: false,"}}
[info] >    offlineMode: false, {"user":"  offlineMode: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   offlineMode: false,"}}
[info] >    emailVerified: true, {"user":"  emailVerified: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   emailVerified: true,"}}
[info] >    createdAt: 1746969090959, {"user":"  createdAt: 1746969090959,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   createdAt: 1746969090959,"}}
[info] >    name: 'Dr. Javad Ahmed', {"user":"  name: 'Dr. Javad Ahmed',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   name: 'Dr. Javad Ahmed',"}}
[info] >    ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ], {"user":"  ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],"}}
[info] >    profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90', {"user":"  profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',"}}
[info] >    lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 }, {"user":"  lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },"}}
[info] >    updatedAt: 1753292227987, {"user":"  updatedAt: 1753292227987,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   updatedAt: 1753292227987,"}}
[info] >    assignedFarmIds: [ {"user":"  assignedFarmIds: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   assignedFarmIds: ["}}
[info] >      'ell4xZlOUxIwbSTdk4gp', {"user":"    'ell4xZlOUxIwbSTdk4gp',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'ell4xZlOUxIwbSTdk4gp',"}}
[info] >      'YrwVdr1vr3KGqtXP3bqX', {"user":"    'YrwVdr1vr3KGqtXP3bqX',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'YrwVdr1vr3KGqtXP3bqX',"}}
[info] >      '7owH6eTfGSYqaRpgIsQE', {"user":"    '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7owH6eTfGSYqaRpgIsQE',"}}
[info] >      'icSTOLwTaC6WYgWjsgDk', {"user":"    'icSTOLwTaC6WYgWjsgDk',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'icSTOLwTaC6WYgWjsgDk',"}}
[info] >      'BtniJTurDAn0saHHbEGF', {"user":"    'BtniJTurDAn0saHHbEGF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'BtniJTurDAn0saHHbEGF',"}}
[info] >      'wFSF3LbHQwPYy6A0pu94', {"user":"    'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'wFSF3LbHQwPYy6A0pu94',"}}
[info] >      'fNgkVoyHni01zmozumbK', {"user":"    'fNgkVoyHni01zmozumbK',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'fNgkVoyHni01zmozumbK',"}}
[info] >      'C6FceATDCJxVtji42Q3D', {"user":"    'C6FceATDCJxVtji42Q3D',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'C6FceATDCJxVtji42Q3D',"}}
[info] >      'M5Pahb6Vi1rdq7KZB5LF', {"user":"    'M5Pahb6Vi1rdq7KZB5LF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'M5Pahb6Vi1rdq7KZB5LF',"}}
[info] >      'oXnGAP4V78vilhIVpIQN', {"user":"    'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'oXnGAP4V78vilhIVpIQN',"}}
[info] >      'NpS3CdrpHvPZClMMDeFc', {"user":"    'NpS3CdrpHvPZClMMDeFc',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'NpS3CdrpHvPZClMMDeFc',"}}
[info] >      'TlS4g9PWKN1wNBtjHEoq', {"user":"    'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >      'rb2n32HuXJlFOk44cAkJ', {"user":"    'rb2n32HuXJlFOk44cAkJ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'rb2n32HuXJlFOk44cAkJ',"}}
[info] >      'UWkNKuXggt70Oe4bkzcx', {"user":"    'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'UWkNKuXggt70Oe4bkzcx',"}}
[info] >      'JRAQxnj2u4vmHNuPHz3W' {"user":"    'JRAQxnj2u4vmHNuPHz3W'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'JRAQxnj2u4vmHNuPHz3W'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  Processing farm selection for editing... {"user":"Processing farm selection for editing...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Processing farm selection for editing..."}}
[info] >  Selected farm ID for editing: Change Location to Dina {"user":"Selected farm ID for editing: Change Location to Dina","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Selected farm ID for editing: Change Location to Dina"}}
[debug] [2025-07-25T12:03:26.476Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 982.268ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 982.268ms"}}
[debug] [2025-07-25T12:03:26.493Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE"}}
[debug] [2025-07-25T12:03:26.493Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-25T12:03:26.494Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-25T12:03:26.495Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:04:59.052Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:04:59.052Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:04:59.053Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:04:59.052Z"],"workRunningCount":1}
[debug] [2025-07-25T12:04:59.053Z] Accepted request OPTIONS /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-25T12:04:59.061Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-25T12:04:59.062Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-25T12:04:59.062Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-25T12:04:59.064Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY"}}
[debug] [2025-07-25T12:04:59.071Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 7.8947ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 7.8947ms"}}
[debug] [2025-07-25T12:04:59.087Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE"}}
[debug] [2025-07-25T12:04:59.087Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-25T12:04:59.090Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-25T12:04:59.091Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:04:59.186Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:04:59.186Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:04:59.186Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:04:59.186Z"],"workRunningCount":1}
[debug] [2025-07-25T12:04:59.186Z] Accepted request POST /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-25T12:04:59.194Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-25T12:04:59.195Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-25T12:04:59.195Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-25T12:04:59.198Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY"}}
[info] >  📨 Context action: edit_farm {"user":"📨 Context action: edit_farm","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: edit_farm"}}
[info] >  📨 Context action: { {"user":"📨 Context action: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: {"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    farms: [ {"user":"  farms: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farms: ["}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: '7owH6eTfGSYqaRpgIsQE', {"user":"      id: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >        name: 'Haven View.', {"user":"      name: 'Haven View.',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Haven View.',"}}
[info] >        location: [Object] {"user":"      location: [Object]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       location: [Object]"}}
[info] >      } {"user":"    }","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     }"}}
[info] >    ], {"user":"  ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ],"}}
[info] >    previousAnimalData: null, {"user":"  previousAnimalData: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   previousAnimalData: null,"}}
[info] >    previousFarmData: null, {"user":"  previousFarmData: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   previousFarmData: null,"}}
[info] >    defaultFarmTypeId: 'B2bye8PZBQYqscxXoVqZ', {"user":"  defaultFarmTypeId: 'B2bye8PZBQYqscxXoVqZ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   defaultFarmTypeId: 'B2bye8PZBQYqscxXoVqZ',"}}
[info] >    defaultStatusId: 'tEbttSFNlpr6gGm5y66l', {"user":"  defaultStatusId: 'tEbttSFNlpr6gGm5y66l',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   defaultStatusId: 'tEbttSFNlpr6gGm5y66l',"}}
[info] >    defaultSizeUnitId: 'ezVe7GdWq6SAxwaREyIw', {"user":"  defaultSizeUnitId: 'ezVe7GdWq6SAxwaREyIw',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   defaultSizeUnitId: 'ezVe7GdWq6SAxwaREyIw',"}}
[info] >    farmLocation: '', {"user":"  farmLocation: '',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farmLocation: '',"}}
[info] >    context: { {"user":"  context: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   context: {"}}
[info] >      action: 'edit_farm', {"user":"    action: 'edit_farm',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     action: 'edit_farm',"}}
[info] >      needsFarmSelection: true, {"user":"    needsFarmSelection: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     needsFarmSelection: true,"}}
[info] >      availableFarms: [ {"user":"    availableFarms: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     availableFarms: ["}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object] {"user":"      [Object], [Object]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object]"}}
[info] >      ] {"user":"    ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     ]"}}
[info] >    }, {"user":"  },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   },"}}
[info] >    prompt: 'Change name to Farm_00034' {"user":"  prompt: 'Change name to Farm_00034'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Change name to Farm_00034'"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[debug] [2025-07-25T12:04:59.993Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 797.0938ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 797.0938ms"}}
[debug] [2025-07-25T12:05:00.011Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE"}}
[debug] [2025-07-25T12:05:00.011Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-25T12:05:00.012Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-25T12:05:00.012Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[info] >  ............................ { {"user":"............................ {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ............................ {"}}
[info] >    id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    email: '<EMAIL>', {"user":"  email: '<EMAIL>',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   email: '<EMAIL>',"}}
[info] >    role: 'owner', {"user":"  role: 'owner',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   role: 'owner',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    preferAudio: false, {"user":"  preferAudio: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   preferAudio: false,"}}
[info] >    offlineMode: false, {"user":"  offlineMode: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   offlineMode: false,"}}
[info] >    emailVerified: true, {"user":"  emailVerified: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   emailVerified: true,"}}
[info] >    createdAt: 1746969090959, {"user":"  createdAt: 1746969090959,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   createdAt: 1746969090959,"}}
[info] >    name: 'Dr. Javad Ahmed', {"user":"  name: 'Dr. Javad Ahmed',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   name: 'Dr. Javad Ahmed',"}}
[info] >    ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ], {"user":"  ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],"}}
[info] >    profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90', {"user":"  profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',"}}
[info] >    lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 }, {"user":"  lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },"}}
[info] >    updatedAt: 1753292227987, {"user":"  updatedAt: 1753292227987,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   updatedAt: 1753292227987,"}}
[info] >    assignedFarmIds: [ {"user":"  assignedFarmIds: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   assignedFarmIds: ["}}
[info] >      'ell4xZlOUxIwbSTdk4gp', {"user":"    'ell4xZlOUxIwbSTdk4gp',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'ell4xZlOUxIwbSTdk4gp',"}}
[info] >      'YrwVdr1vr3KGqtXP3bqX', {"user":"    'YrwVdr1vr3KGqtXP3bqX',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'YrwVdr1vr3KGqtXP3bqX',"}}
[info] >      '7owH6eTfGSYqaRpgIsQE', {"user":"    '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7owH6eTfGSYqaRpgIsQE',"}}
[info] >      'icSTOLwTaC6WYgWjsgDk', {"user":"    'icSTOLwTaC6WYgWjsgDk',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'icSTOLwTaC6WYgWjsgDk',"}}
[info] >      'BtniJTurDAn0saHHbEGF', {"user":"    'BtniJTurDAn0saHHbEGF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'BtniJTurDAn0saHHbEGF',"}}
[info] >      'wFSF3LbHQwPYy6A0pu94', {"user":"    'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'wFSF3LbHQwPYy6A0pu94',"}}
[info] >      'fNgkVoyHni01zmozumbK', {"user":"    'fNgkVoyHni01zmozumbK',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'fNgkVoyHni01zmozumbK',"}}
[info] >      'C6FceATDCJxVtji42Q3D', {"user":"    'C6FceATDCJxVtji42Q3D',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'C6FceATDCJxVtji42Q3D',"}}
[info] >      'M5Pahb6Vi1rdq7KZB5LF', {"user":"    'M5Pahb6Vi1rdq7KZB5LF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'M5Pahb6Vi1rdq7KZB5LF',"}}
[info] >      'oXnGAP4V78vilhIVpIQN', {"user":"    'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'oXnGAP4V78vilhIVpIQN',"}}
[info] >      'NpS3CdrpHvPZClMMDeFc', {"user":"    'NpS3CdrpHvPZClMMDeFc',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'NpS3CdrpHvPZClMMDeFc',"}}
[info] >      'TlS4g9PWKN1wNBtjHEoq', {"user":"    'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >      'rb2n32HuXJlFOk44cAkJ', {"user":"    'rb2n32HuXJlFOk44cAkJ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'rb2n32HuXJlFOk44cAkJ',"}}
[info] >      'UWkNKuXggt70Oe4bkzcx', {"user":"    'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'UWkNKuXggt70Oe4bkzcx',"}}
[info] >      'JRAQxnj2u4vmHNuPHz3W' {"user":"    'JRAQxnj2u4vmHNuPHz3W'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'JRAQxnj2u4vmHNuPHz3W'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  Processing farm selection for editing... {"user":"Processing farm selection for editing...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Processing farm selection for editing..."}}
[info] >  Selected farm ID for editing: Change name to Farm_00034 {"user":"Selected farm ID for editing: Change name to Farm_00034","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Selected farm ID for editing: Change name to Farm_00034"}}
[debug] [2025-07-25T12:10:47.202Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:10:47.202Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:10:47.204Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:10:47.202Z"],"workRunningCount":1}
[debug] [2025-07-25T12:10:47.205Z] Accepted request OPTIONS /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-25T12:10:47.221Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-25T12:10:47.223Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-25T12:10:47.223Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-25T12:10:47.228Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY"}}
[debug] [2025-07-25T12:10:47.238Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 9.9071ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 9.9071ms"}}
[debug] [2025-07-25T12:10:47.239Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE"}}
[debug] [2025-07-25T12:10:47.240Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-25T12:10:47.240Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-25T12:10:47.240Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:10:47.387Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:10:47.387Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:10:47.387Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:10:47.387Z"],"workRunningCount":1}
[debug] [2025-07-25T12:10:47.387Z] Accepted request POST /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-25T12:10:47.402Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-25T12:10:47.406Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-25T12:10:47.407Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-25T12:10:47.419Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY"}}
[info] >  📨 Context action: edit_farm {"user":"📨 Context action: edit_farm","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: edit_farm"}}
[info] >  📨 Context action: { {"user":"📨 Context action: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: {"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    farms: [ {"user":"  farms: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farms: ["}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: '7owH6eTfGSYqaRpgIsQE', {"user":"      id: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >        name: 'Haven View.', {"user":"      name: 'Haven View.',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Haven View.',"}}
[info] >        location: [Object] {"user":"      location: [Object]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       location: [Object]"}}
[info] >      } {"user":"    }","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     }"}}
[info] >    ], {"user":"  ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ],"}}
[info] >    previousAnimalData: null, {"user":"  previousAnimalData: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   previousAnimalData: null,"}}
[info] >    previousFarmData: null, {"user":"  previousFarmData: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   previousFarmData: null,"}}
[info] >    defaultFarmTypeId: 'B2bye8PZBQYqscxXoVqZ', {"user":"  defaultFarmTypeId: 'B2bye8PZBQYqscxXoVqZ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   defaultFarmTypeId: 'B2bye8PZBQYqscxXoVqZ',"}}
[info] >    defaultStatusId: 'tEbttSFNlpr6gGm5y66l', {"user":"  defaultStatusId: 'tEbttSFNlpr6gGm5y66l',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   defaultStatusId: 'tEbttSFNlpr6gGm5y66l',"}}
[info] >    defaultSizeUnitId: 'ezVe7GdWq6SAxwaREyIw', {"user":"  defaultSizeUnitId: 'ezVe7GdWq6SAxwaREyIw',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   defaultSizeUnitId: 'ezVe7GdWq6SAxwaREyIw',"}}
[info] >    farmLocation: '', {"user":"  farmLocation: '',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farmLocation: '',"}}
[info] >    context: { {"user":"  context: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   context: {"}}
[info] >      action: 'edit_farm', {"user":"    action: 'edit_farm',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     action: 'edit_farm',"}}
[info] >      needsFarmSelection: true, {"user":"    needsFarmSelection: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     needsFarmSelection: true,"}}
[info] >      availableFarms: [ {"user":"    availableFarms: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     availableFarms: ["}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object] {"user":"      [Object], [Object]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object]"}}
[info] >      ] {"user":"    ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     ]"}}
[info] >    }, {"user":"  },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   },"}}
[info] >    prompt: 'GIve me all detail' {"user":"  prompt: 'GIve me all detail'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'GIve me all detail'"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  ............................ { {"user":"............................ {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ............................ {"}}
[info] >    id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    email: '<EMAIL>', {"user":"  email: '<EMAIL>',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   email: '<EMAIL>',"}}
[info] >    role: 'owner', {"user":"  role: 'owner',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   role: 'owner',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    preferAudio: false, {"user":"  preferAudio: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   preferAudio: false,"}}
[info] >    offlineMode: false, {"user":"  offlineMode: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   offlineMode: false,"}}
[info] >    emailVerified: true, {"user":"  emailVerified: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   emailVerified: true,"}}
[info] >    createdAt: 1746969090959, {"user":"  createdAt: 1746969090959,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   createdAt: 1746969090959,"}}
[info] >    name: 'Dr. Javad Ahmed', {"user":"  name: 'Dr. Javad Ahmed',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   name: 'Dr. Javad Ahmed',"}}
[info] >    ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ], {"user":"  ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],"}}
[info] >    profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90', {"user":"  profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',"}}
[info] >    lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 }, {"user":"  lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },"}}
[info] >    updatedAt: 1753292227987, {"user":"  updatedAt: 1753292227987,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   updatedAt: 1753292227987,"}}
[info] >    assignedFarmIds: [ {"user":"  assignedFarmIds: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   assignedFarmIds: ["}}
[info] >      'ell4xZlOUxIwbSTdk4gp', {"user":"    'ell4xZlOUxIwbSTdk4gp',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'ell4xZlOUxIwbSTdk4gp',"}}
[info] >      'YrwVdr1vr3KGqtXP3bqX', {"user":"    'YrwVdr1vr3KGqtXP3bqX',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'YrwVdr1vr3KGqtXP3bqX',"}}
[info] >      '7owH6eTfGSYqaRpgIsQE', {"user":"    '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7owH6eTfGSYqaRpgIsQE',"}}
[info] >      'icSTOLwTaC6WYgWjsgDk', {"user":"    'icSTOLwTaC6WYgWjsgDk',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'icSTOLwTaC6WYgWjsgDk',"}}
[info] >      'BtniJTurDAn0saHHbEGF', {"user":"    'BtniJTurDAn0saHHbEGF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'BtniJTurDAn0saHHbEGF',"}}
[info] >      'wFSF3LbHQwPYy6A0pu94', {"user":"    'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'wFSF3LbHQwPYy6A0pu94',"}}
[info] >      'fNgkVoyHni01zmozumbK', {"user":"    'fNgkVoyHni01zmozumbK',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'fNgkVoyHni01zmozumbK',"}}
[info] >      'C6FceATDCJxVtji42Q3D', {"user":"    'C6FceATDCJxVtji42Q3D',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'C6FceATDCJxVtji42Q3D',"}}
[info] >      'M5Pahb6Vi1rdq7KZB5LF', {"user":"    'M5Pahb6Vi1rdq7KZB5LF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'M5Pahb6Vi1rdq7KZB5LF',"}}
[info] >      'oXnGAP4V78vilhIVpIQN', {"user":"    'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'oXnGAP4V78vilhIVpIQN',"}}
[info] >      'NpS3CdrpHvPZClMMDeFc', {"user":"    'NpS3CdrpHvPZClMMDeFc',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'NpS3CdrpHvPZClMMDeFc',"}}
[info] >      'TlS4g9PWKN1wNBtjHEoq', {"user":"    'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >      'rb2n32HuXJlFOk44cAkJ', {"user":"    'rb2n32HuXJlFOk44cAkJ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'rb2n32HuXJlFOk44cAkJ',"}}
[info] >      'UWkNKuXggt70Oe4bkzcx', {"user":"    'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'UWkNKuXggt70Oe4bkzcx',"}}
[info] >      'JRAQxnj2u4vmHNuPHz3W' {"user":"    'JRAQxnj2u4vmHNuPHz3W'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'JRAQxnj2u4vmHNuPHz3W'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[debug] [2025-07-25T12:10:49.259Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 1839.346ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 1839.346ms"}}
[debug] [2025-07-25T12:10:49.272Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE"}}
[debug] [2025-07-25T12:10:49.272Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-25T12:10:49.272Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-25T12:10:49.272Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[info] >  Processing farm selection for editing... {"user":"Processing farm selection for editing...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Processing farm selection for editing..."}}
[info] >  Selected farm ID for editing: GIve me all detail {"user":"Selected farm ID for editing: GIve me all detail","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Selected farm ID for editing: GIve me all detail"}}
[debug] [2025-07-25T12:11:44.701Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:11:44.701Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:11:44.702Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:11:44.701Z"],"workRunningCount":1}
[debug] [2025-07-25T12:11:44.702Z] Accepted request OPTIONS /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-25T12:11:44.713Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-25T12:11:44.714Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-25T12:11:44.714Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-25T12:11:44.942Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY"}}
[debug] [2025-07-25T12:11:44.948Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 6.8755ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 6.8755ms"}}
[debug] [2025-07-25T12:11:45.068Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE"}}
[debug] [2025-07-25T12:11:45.071Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-25T12:11:45.072Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-25T12:11:45.072Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:11:45.073Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:11:45.073Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:11:45.074Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:11:45.073Z"],"workRunningCount":1}
[debug] [2025-07-25T12:11:45.074Z] Accepted request POST /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-25T12:11:45.098Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-25T12:11:45.100Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-25T12:11:45.100Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-25T12:11:45.406Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY"}}
[info] >  ............................ { {"user":"............................ {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ............................ {"}}
[info] >    id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    email: '<EMAIL>', {"user":"  email: '<EMAIL>',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   email: '<EMAIL>',"}}
[info] >    role: 'owner', {"user":"  role: 'owner',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   role: 'owner',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    preferAudio: false, {"user":"  preferAudio: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   preferAudio: false,"}}
[info] >    offlineMode: false, {"user":"  offlineMode: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   offlineMode: false,"}}
[info] >    emailVerified: true, {"user":"  emailVerified: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   emailVerified: true,"}}
[info] >    createdAt: 1746969090959, {"user":"  createdAt: 1746969090959,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   createdAt: 1746969090959,"}}
[info] >    name: 'Dr. Javad Ahmed', {"user":"  name: 'Dr. Javad Ahmed',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   name: 'Dr. Javad Ahmed',"}}
[info] >    ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ], {"user":"  ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],"}}
[info] >    profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90', {"user":"  profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',"}}
[info] >    lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 }, {"user":"  lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },"}}
[info] >    updatedAt: 1753292227987, {"user":"  updatedAt: 1753292227987,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   updatedAt: 1753292227987,"}}
[info] >    assignedFarmIds: [ {"user":"  assignedFarmIds: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   assignedFarmIds: ["}}
[info] >      'ell4xZlOUxIwbSTdk4gp', {"user":"    'ell4xZlOUxIwbSTdk4gp',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'ell4xZlOUxIwbSTdk4gp',"}}
[info] >      'YrwVdr1vr3KGqtXP3bqX', {"user":"    'YrwVdr1vr3KGqtXP3bqX',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'YrwVdr1vr3KGqtXP3bqX',"}}
[info] >      '7owH6eTfGSYqaRpgIsQE', {"user":"    '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7owH6eTfGSYqaRpgIsQE',"}}
[info] >      'icSTOLwTaC6WYgWjsgDk', {"user":"    'icSTOLwTaC6WYgWjsgDk',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'icSTOLwTaC6WYgWjsgDk',"}}
[info] >      'BtniJTurDAn0saHHbEGF', {"user":"    'BtniJTurDAn0saHHbEGF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'BtniJTurDAn0saHHbEGF',"}}
[info] >      'wFSF3LbHQwPYy6A0pu94', {"user":"    'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'wFSF3LbHQwPYy6A0pu94',"}}
[info] >      'fNgkVoyHni01zmozumbK', {"user":"    'fNgkVoyHni01zmozumbK',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'fNgkVoyHni01zmozumbK',"}}
[info] >      'C6FceATDCJxVtji42Q3D', {"user":"    'C6FceATDCJxVtji42Q3D',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'C6FceATDCJxVtji42Q3D',"}}
[info] >      'M5Pahb6Vi1rdq7KZB5LF', {"user":"    'M5Pahb6Vi1rdq7KZB5LF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'M5Pahb6Vi1rdq7KZB5LF',"}}
[info] >      'oXnGAP4V78vilhIVpIQN', {"user":"    'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'oXnGAP4V78vilhIVpIQN',"}}
[info] >      'NpS3CdrpHvPZClMMDeFc', {"user":"    'NpS3CdrpHvPZClMMDeFc',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'NpS3CdrpHvPZClMMDeFc',"}}
[info] >      'TlS4g9PWKN1wNBtjHEoq', {"user":"    'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >      'rb2n32HuXJlFOk44cAkJ', {"user":"    'rb2n32HuXJlFOk44cAkJ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'rb2n32HuXJlFOk44cAkJ',"}}
[info] >      'UWkNKuXggt70Oe4bkzcx', {"user":"    'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'UWkNKuXggt70Oe4bkzcx',"}}
[info] >      'JRAQxnj2u4vmHNuPHz3W' {"user":"    'JRAQxnj2u4vmHNuPHz3W'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'JRAQxnj2u4vmHNuPHz3W'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[debug] [2025-07-25T12:11:50.537Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 5131.5045ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 5131.5045ms"}}
[debug] [2025-07-25T12:11:50.656Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE"}}
[debug] [2025-07-25T12:11:50.656Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-25T12:11:50.657Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-25T12:11:50.657Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[info] >  ✏️ GENERAL EDIT PATTERN MATCHED! {"user":"✏️ GENERAL EDIT PATTERN MATCHED!","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ✏️ GENERAL EDIT PATTERN MATCHED!"}}
[info] >  requestType edit_general {"user":"requestType edit_general","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m requestType edit_general"}}
[info] >  📨 Context received: null {"user":"📨 Context received: null","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context received: null"}}
[info] >  📨 Context action: undefined {"user":"📨 Context action: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: undefined"}}
[info] >  📨 Context needsAnimalSelection: undefined {"user":"📨 Context needsAnimalSelection: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context needsAnimalSelection: undefined"}}
[info] >  Processing general edit request - showing module selection... {"user":"Processing general edit request - showing module selection...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Processing general edit request - showing module selection..."}}
[debug] [2025-07-25T12:17:19.059Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:17:19.059Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:17:19.061Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:17:19.059Z"],"workRunningCount":1}
[debug] [2025-07-25T12:17:19.061Z] Accepted request OPTIONS /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-25T12:17:19.070Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-25T12:17:19.070Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-25T12:17:19.071Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-25T12:17:19.086Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY"}}
[debug] [2025-07-25T12:17:19.092Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 6.7004ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 6.7004ms"}}
[debug] [2025-07-25T12:17:19.137Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE"}}
[debug] [2025-07-25T12:17:19.137Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-25T12:17:19.137Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-25T12:17:19.138Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:17:19.190Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:17:19.190Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:17:19.191Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:17:19.190Z"],"workRunningCount":1}
[debug] [2025-07-25T12:17:19.191Z] Accepted request POST /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-25T12:17:19.199Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-25T12:17:19.200Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-25T12:17:19.200Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-25T12:17:19.201Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY"}}
[info] >  📨 Context action: select_edit_module {"user":"📨 Context action: select_edit_module","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: select_edit_module"}}
[info] >  📨 Context action: { {"user":"📨 Context action: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: {"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    farms: [ {"user":"  farms: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farms: ["}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: '7owH6eTfGSYqaRpgIsQE', {"user":"      id: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >        name: 'Haven View.', {"user":"      name: 'Haven View.',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Haven View.',"}}
[info] >        location: [Object] {"user":"      location: [Object]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       location: [Object]"}}
[info] >      } {"user":"    }","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     }"}}
[info] >    ], {"user":"  ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ],"}}
[info] >    previousAnimalData: null, {"user":"  previousAnimalData: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   previousAnimalData: null,"}}
[info] >    previousFarmData: null, {"user":"  previousFarmData: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   previousFarmData: null,"}}
[info] >    defaultFarmTypeId: 'B2bye8PZBQYqscxXoVqZ', {"user":"  defaultFarmTypeId: 'B2bye8PZBQYqscxXoVqZ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   defaultFarmTypeId: 'B2bye8PZBQYqscxXoVqZ',"}}
[info] >    defaultStatusId: 'tEbttSFNlpr6gGm5y66l', {"user":"  defaultStatusId: 'tEbttSFNlpr6gGm5y66l',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   defaultStatusId: 'tEbttSFNlpr6gGm5y66l',"}}
[info] >    defaultSizeUnitId: 'ezVe7GdWq6SAxwaREyIw', {"user":"  defaultSizeUnitId: 'ezVe7GdWq6SAxwaREyIw',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   defaultSizeUnitId: 'ezVe7GdWq6SAxwaREyIw',"}}
[info] >    farmLocation: '', {"user":"  farmLocation: '',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farmLocation: '',"}}
[info] >    context: { action: 'select_edit_module', needsModuleSelection: true }, {"user":"  context: { action: 'select_edit_module', needsModuleSelection: true },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   context: { action: 'select_edit_module', needsModuleSelection: true },"}}
[info] >    prompt: 'Edit farm' {"user":"  prompt: 'Edit farm'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Edit farm'"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  ............................ { {"user":"............................ {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ............................ {"}}
[info] >    id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    email: '<EMAIL>', {"user":"  email: '<EMAIL>',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   email: '<EMAIL>',"}}
[info] >    role: 'owner', {"user":"  role: 'owner',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   role: 'owner',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    preferAudio: false, {"user":"  preferAudio: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   preferAudio: false,"}}
[info] >    offlineMode: false, {"user":"  offlineMode: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   offlineMode: false,"}}
[info] >    emailVerified: true, {"user":"  emailVerified: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   emailVerified: true,"}}
[info] >    createdAt: 1746969090959, {"user":"  createdAt: 1746969090959,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   createdAt: 1746969090959,"}}
[info] >    name: 'Dr. Javad Ahmed', {"user":"  name: 'Dr. Javad Ahmed',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   name: 'Dr. Javad Ahmed',"}}
[info] >    ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ], {"user":"  ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],"}}
[info] >    profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90', {"user":"  profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',"}}
[info] >    lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 }, {"user":"  lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },"}}
[info] >    updatedAt: 1753292227987, {"user":"  updatedAt: 1753292227987,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   updatedAt: 1753292227987,"}}
[info] >    assignedFarmIds: [ {"user":"  assignedFarmIds: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   assignedFarmIds: ["}}
[info] >      'ell4xZlOUxIwbSTdk4gp', {"user":"    'ell4xZlOUxIwbSTdk4gp',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'ell4xZlOUxIwbSTdk4gp',"}}
[info] >      'YrwVdr1vr3KGqtXP3bqX', {"user":"    'YrwVdr1vr3KGqtXP3bqX',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'YrwVdr1vr3KGqtXP3bqX',"}}
[info] >      '7owH6eTfGSYqaRpgIsQE', {"user":"    '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7owH6eTfGSYqaRpgIsQE',"}}
[info] >      'icSTOLwTaC6WYgWjsgDk', {"user":"    'icSTOLwTaC6WYgWjsgDk',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'icSTOLwTaC6WYgWjsgDk',"}}
[info] >      'BtniJTurDAn0saHHbEGF', {"user":"    'BtniJTurDAn0saHHbEGF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'BtniJTurDAn0saHHbEGF',"}}
[info] >      'wFSF3LbHQwPYy6A0pu94', {"user":"    'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'wFSF3LbHQwPYy6A0pu94',"}}
[info] >      'fNgkVoyHni01zmozumbK', {"user":"    'fNgkVoyHni01zmozumbK',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'fNgkVoyHni01zmozumbK',"}}
[info] >      'C6FceATDCJxVtji42Q3D', {"user":"    'C6FceATDCJxVtji42Q3D',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'C6FceATDCJxVtji42Q3D',"}}
[info] >      'M5Pahb6Vi1rdq7KZB5LF', {"user":"    'M5Pahb6Vi1rdq7KZB5LF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'M5Pahb6Vi1rdq7KZB5LF',"}}
[info] >      'oXnGAP4V78vilhIVpIQN', {"user":"    'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'oXnGAP4V78vilhIVpIQN',"}}
[info] >      'NpS3CdrpHvPZClMMDeFc', {"user":"    'NpS3CdrpHvPZClMMDeFc',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'NpS3CdrpHvPZClMMDeFc',"}}
[info] >      'TlS4g9PWKN1wNBtjHEoq', {"user":"    'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >      'rb2n32HuXJlFOk44cAkJ', {"user":"    'rb2n32HuXJlFOk44cAkJ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'rb2n32HuXJlFOk44cAkJ',"}}
[info] >      'UWkNKuXggt70Oe4bkzcx', {"user":"    'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'UWkNKuXggt70Oe4bkzcx',"}}
[info] >      'JRAQxnj2u4vmHNuPHz3W' {"user":"    'JRAQxnj2u4vmHNuPHz3W'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'JRAQxnj2u4vmHNuPHz3W'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  ✏️ FARM EDIT PATTERN MATCHED! {"user":"✏️ FARM EDIT PATTERN MATCHED!","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ✏️ FARM EDIT PATTERN MATCHED!"}}
[info] >  requestType edit_farm {"user":"requestType edit_farm","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m requestType edit_farm"}}
[info] >  Processing farm edit request... {"user":"Processing farm edit request...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Processing farm edit request..."}}
[info] >  Farm edit analysis: { {"user":"Farm edit analysis: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Farm edit analysis: {"}}
[info] >    isEditRequest: true, {"user":"  isEditRequest: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   isEditRequest: true,"}}
[info] >    action: 'edit_farm', {"user":"  action: 'edit_farm',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   action: 'edit_farm',"}}
[info] >    farmName: null, {"user":"  farmName: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farmName: null,"}}
[info] >    fieldToUpdate: null, {"user":"  fieldToUpdate: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   fieldToUpdate: null,"}}
[info] >    newValue: null, {"user":"  newValue: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   newValue: null,"}}
[info] >    confidence: 70, {"user":"  confidence: 70,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   confidence: 70,"}}
[info] >    language: 'en' {"user":"  language: 'en'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en'"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[debug] [2025-07-25T12:17:25.496Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 6295.9718ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 6295.9718ms"}}
[debug] [2025-07-25T12:17:25.501Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE"}}
[debug] [2025-07-25T12:17:25.503Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:17:25.504Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[debug] [2025-07-25T12:17:25.509Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[debug] [2025-07-25T12:17:25.512Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[debug] [2025-07-25T12:17:25.515Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[debug] [2025-07-25T12:17:25.517Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[debug] [2025-07-25T12:17:25.520Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[debug] [2025-07-25T12:17:25.523Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[debug] [2025-07-25T12:17:25.524Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-25T12:17:25.525Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-25T12:17:34.919Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:17:34.918Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:17:34.919Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:17:34.918Z"],"workRunningCount":1}
[debug] [2025-07-25T12:17:34.920Z] Accepted request OPTIONS /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-25T12:17:34.935Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-25T12:17:34.936Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-25T12:17:34.937Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-25T12:17:34.965Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY"}}
[debug] [2025-07-25T12:17:34.970Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 5.088ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 5.088ms"}}
[debug] [2025-07-25T12:17:35.003Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE"}}
[debug] [2025-07-25T12:17:35.003Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-25T12:17:35.003Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-25T12:17:35.004Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:17:35.066Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:17:35.066Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:17:35.067Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:17:35.066Z"],"workRunningCount":1}
[debug] [2025-07-25T12:17:35.067Z] Accepted request POST /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-25T12:17:35.071Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-25T12:17:35.072Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-25T12:17:35.072Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-25T12:17:35.079Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY"}}
[info] >  📨 Context action: edit_farm {"user":"📨 Context action: edit_farm","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: edit_farm"}}
[info] >  📨 Context action: { {"user":"📨 Context action: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: {"}}
[info] >    prompt: 'Selected farm: Farm_003', {"user":"  prompt: 'Selected farm: Farm_003',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Selected farm: Farm_003',"}}
[info] >    selectedFarmId: 'JRAQxnj2u4vmHNuPHz3W', {"user":"  selectedFarmId: 'JRAQxnj2u4vmHNuPHz3W',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   selectedFarmId: 'JRAQxnj2u4vmHNuPHz3W',"}}
[info] >    userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    context: { {"user":"  context: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   context: {"}}
[info] >      action: 'edit_farm', {"user":"    action: 'edit_farm',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     action: 'edit_farm',"}}
[info] >      needsFarmSelection: true, {"user":"    needsFarmSelection: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     needsFarmSelection: true,"}}
[info] >      availableFarms: [ {"user":"    availableFarms: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     availableFarms: ["}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object] {"user":"      [Object], [Object]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object]"}}
[info] >      ] {"user":"    ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     ]"}}
[info] >    }, {"user":"  },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   },"}}
[info] >    farms: [ {"user":"  farms: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farms: ["}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: '7owH6eTfGSYqaRpgIsQE', {"user":"      id: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >        name: 'Haven View.', {"user":"      name: 'Haven View.',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Haven View.',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'BtniJTurDAn0saHHbEGF', {"user":"      id: 'BtniJTurDAn0saHHbEGF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'BtniJTurDAn0saHHbEGF',"}}
[info] >        name: 'Green Land..', {"user":"      name: 'Green Land..',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Green Land..',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'wFSF3LbHQwPYy6A0pu94', {"user":"      id: 'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'wFSF3LbHQwPYy6A0pu94',"}}
[info] >        name: 'Dairy House ', {"user":"      name: 'Dairy House ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Dairy House ',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'fNgkVoyHni01zmozumbK', {"user":"      id: 'fNgkVoyHni01zmozumbK',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'fNgkVoyHni01zmozumbK',"}}
[info] >        name: 'Octans Farm', {"user":"      name: 'Octans Farm',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Octans Farm',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'oXnGAP4V78vilhIVpIQN', {"user":"      id: 'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'oXnGAP4V78vilhIVpIQN',"}}
[info] >        name: 'Trade Farm', {"user":"      name: 'Trade Farm',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Trade Farm',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'NpS3CdrpHvPZClMMDeFc', {"user":"      id: 'NpS3CdrpHvPZClMMDeFc',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'NpS3CdrpHvPZClMMDeFc',"}}
[info] >        name: 'River view ', {"user":"      name: 'River view ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'River view ',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'TlS4g9PWKN1wNBtjHEoq', {"user":"      id: 'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >        name: 'Sabz Chara', {"user":"      name: 'Sabz Chara',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Sabz Chara',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'rb2n32HuXJlFOk44cAkJ', {"user":"      id: 'rb2n32HuXJlFOk44cAkJ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'rb2n32HuXJlFOk44cAkJ',"}}
[info] >        name: 'Farm_002', {"user":"      name: 'Farm_002',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Farm_002',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'UWkNKuXggt70Oe4bkzcx', {"user":"      id: 'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'UWkNKuXggt70Oe4bkzcx',"}}
[info] >        name: 'Farm_003', {"user":"      name: 'Farm_003',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Farm_003',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'JRAQxnj2u4vmHNuPHz3W', {"user":"      id: 'JRAQxnj2u4vmHNuPHz3W',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'JRAQxnj2u4vmHNuPHz3W',"}}
[info] >        name: 'Farm_003', {"user":"      name: 'Farm_003',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Farm_003',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      } {"user":"    }","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     }"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  ............................ { {"user":"............................ {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ............................ {"}}
[info] >    id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    email: '<EMAIL>', {"user":"  email: '<EMAIL>',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   email: '<EMAIL>',"}}
[info] >    role: 'owner', {"user":"  role: 'owner',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   role: 'owner',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    preferAudio: false, {"user":"  preferAudio: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   preferAudio: false,"}}
[info] >    offlineMode: false, {"user":"  offlineMode: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   offlineMode: false,"}}
[info] >    emailVerified: true, {"user":"  emailVerified: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   emailVerified: true,"}}
[info] >    createdAt: 1746969090959, {"user":"  createdAt: 1746969090959,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   createdAt: 1746969090959,"}}
[info] >    name: 'Dr. Javad Ahmed', {"user":"  name: 'Dr. Javad Ahmed',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   name: 'Dr. Javad Ahmed',"}}
[info] >    ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ], {"user":"  ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],"}}
[info] >    profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90', {"user":"  profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',"}}
[info] >    lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 }, {"user":"  lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },"}}
[info] >    updatedAt: 1753292227987, {"user":"  updatedAt: 1753292227987,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   updatedAt: 1753292227987,"}}
[info] >    assignedFarmIds: [ {"user":"  assignedFarmIds: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   assignedFarmIds: ["}}
[info] >      'ell4xZlOUxIwbSTdk4gp', {"user":"    'ell4xZlOUxIwbSTdk4gp',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'ell4xZlOUxIwbSTdk4gp',"}}
[info] >      'YrwVdr1vr3KGqtXP3bqX', {"user":"    'YrwVdr1vr3KGqtXP3bqX',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'YrwVdr1vr3KGqtXP3bqX',"}}
[info] >      '7owH6eTfGSYqaRpgIsQE', {"user":"    '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7owH6eTfGSYqaRpgIsQE',"}}
[info] >      'icSTOLwTaC6WYgWjsgDk', {"user":"    'icSTOLwTaC6WYgWjsgDk',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'icSTOLwTaC6WYgWjsgDk',"}}
[info] >      'BtniJTurDAn0saHHbEGF', {"user":"    'BtniJTurDAn0saHHbEGF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'BtniJTurDAn0saHHbEGF',"}}
[info] >      'wFSF3LbHQwPYy6A0pu94', {"user":"    'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'wFSF3LbHQwPYy6A0pu94',"}}
[info] >      'fNgkVoyHni01zmozumbK', {"user":"    'fNgkVoyHni01zmozumbK',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'fNgkVoyHni01zmozumbK',"}}
[info] >      'C6FceATDCJxVtji42Q3D', {"user":"    'C6FceATDCJxVtji42Q3D',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'C6FceATDCJxVtji42Q3D',"}}
[info] >      'M5Pahb6Vi1rdq7KZB5LF', {"user":"    'M5Pahb6Vi1rdq7KZB5LF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'M5Pahb6Vi1rdq7KZB5LF',"}}
[info] >      'oXnGAP4V78vilhIVpIQN', {"user":"    'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'oXnGAP4V78vilhIVpIQN',"}}
[info] >      'NpS3CdrpHvPZClMMDeFc', {"user":"    'NpS3CdrpHvPZClMMDeFc',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'NpS3CdrpHvPZClMMDeFc',"}}
[info] >      'TlS4g9PWKN1wNBtjHEoq', {"user":"    'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >      'rb2n32HuXJlFOk44cAkJ', {"user":"    'rb2n32HuXJlFOk44cAkJ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'rb2n32HuXJlFOk44cAkJ',"}}
[info] >      'UWkNKuXggt70Oe4bkzcx', {"user":"    'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'UWkNKuXggt70Oe4bkzcx',"}}
[info] >      'JRAQxnj2u4vmHNuPHz3W' {"user":"    'JRAQxnj2u4vmHNuPHz3W'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'JRAQxnj2u4vmHNuPHz3W'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[debug] [2025-07-25T12:17:35.587Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 508.1927ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 508.1927ms"}}
[debug] [2025-07-25T12:17:35.589Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE"}}
[debug] [2025-07-25T12:17:35.594Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-25T12:17:35.594Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-25T12:17:35.594Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[info] >  Processing farm selection for editing... {"user":"Processing farm selection for editing...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Processing farm selection for editing..."}}
[info] >  Selected farm ID for editing: JRAQxnj2u4vmHNuPHz3W {"user":"Selected farm ID for editing: JRAQxnj2u4vmHNuPHz3W","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Selected farm ID for editing: JRAQxnj2u4vmHNuPHz3W"}}
[debug] [2025-07-25T12:17:56.891Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:17:56.890Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:17:56.893Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:17:56.890Z"],"workRunningCount":1}
[debug] [2025-07-25T12:17:56.894Z] Accepted request OPTIONS /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-25T12:17:56.915Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-25T12:17:56.916Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-25T12:17:56.916Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-25T12:17:56.927Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY"}}
[debug] [2025-07-25T12:17:56.945Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 18.7059ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 18.7059ms"}}
[debug] [2025-07-25T12:17:56.948Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE"}}
[debug] [2025-07-25T12:17:56.948Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-25T12:17:56.950Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-25T12:17:56.951Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:17:57.097Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:17:57.097Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:17:57.098Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:17:57.097Z"],"workRunningCount":1}
[debug] [2025-07-25T12:17:57.098Z] Accepted request POST /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-25T12:17:57.102Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-25T12:17:57.102Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-25T12:17:57.103Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-25T12:17:57.110Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: BUSY"}}
[info] >  📨 Context action: edit_farm {"user":"📨 Context action: edit_farm","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: edit_farm"}}
[info] >  📨 Context action: { {"user":"📨 Context action: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: {"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    farms: [ {"user":"  farms: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farms: ["}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: '7owH6eTfGSYqaRpgIsQE', {"user":"      id: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >        name: 'Haven View.', {"user":"      name: 'Haven View.',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Haven View.',"}}
[info] >        location: [Object] {"user":"      location: [Object]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       location: [Object]"}}
[info] >      } {"user":"    }","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     }"}}
[info] >    ], {"user":"  ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ],"}}
[info] >    previousAnimalData: null, {"user":"  previousAnimalData: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   previousAnimalData: null,"}}
[info] >    previousFarmData: null, {"user":"  previousFarmData: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   previousFarmData: null,"}}
[info] >    defaultFarmTypeId: 'B2bye8PZBQYqscxXoVqZ', {"user":"  defaultFarmTypeId: 'B2bye8PZBQYqscxXoVqZ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   defaultFarmTypeId: 'B2bye8PZBQYqscxXoVqZ',"}}
[info] >    defaultStatusId: 'tEbttSFNlpr6gGm5y66l', {"user":"  defaultStatusId: 'tEbttSFNlpr6gGm5y66l',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   defaultStatusId: 'tEbttSFNlpr6gGm5y66l',"}}
[info] >    defaultSizeUnitId: 'ezVe7GdWq6SAxwaREyIw', {"user":"  defaultSizeUnitId: 'ezVe7GdWq6SAxwaREyIw',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   defaultSizeUnitId: 'ezVe7GdWq6SAxwaREyIw',"}}
[info] >    farmLocation: '', {"user":"  farmLocation: '',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farmLocation: '',"}}
[info] >    context: { {"user":"  context: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   context: {"}}
[info] >      action: 'edit_farm', {"user":"    action: 'edit_farm',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     action: 'edit_farm',"}}
[info] >      needsFarmSelection: true, {"user":"    needsFarmSelection: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     needsFarmSelection: true,"}}
[info] >      availableFarms: [ {"user":"    availableFarms: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     availableFarms: ["}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object] {"user":"      [Object], [Object]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object]"}}
[info] >      ] {"user":"    ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     ]"}}
[info] >    }, {"user":"  },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   },"}}
[info] >    prompt: 'Give me all detail i want to edit all fields' {"user":"  prompt: 'Give me all detail i want to edit all fields'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Give me all detail i want to edit all fields'"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  ............................ { {"user":"............................ {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ............................ {"}}
[info] >    id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    email: '<EMAIL>', {"user":"  email: '<EMAIL>',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   email: '<EMAIL>',"}}
[info] >    role: 'owner', {"user":"  role: 'owner',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   role: 'owner',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    preferAudio: false, {"user":"  preferAudio: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   preferAudio: false,"}}
[info] >    offlineMode: false, {"user":"  offlineMode: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   offlineMode: false,"}}
[info] >    emailVerified: true, {"user":"  emailVerified: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   emailVerified: true,"}}
[info] >    createdAt: 1746969090959, {"user":"  createdAt: 1746969090959,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   createdAt: 1746969090959,"}}
[info] >    name: 'Dr. Javad Ahmed', {"user":"  name: 'Dr. Javad Ahmed',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   name: 'Dr. Javad Ahmed',"}}
[info] >    ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ], {"user":"  ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],"}}
[info] >    profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90', {"user":"  profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',"}}
[info] >    lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 }, {"user":"  lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },"}}
[info] >    updatedAt: 1753292227987, {"user":"  updatedAt: 1753292227987,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   updatedAt: 1753292227987,"}}
[info] >    assignedFarmIds: [ {"user":"  assignedFarmIds: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   assignedFarmIds: ["}}
[info] >      'ell4xZlOUxIwbSTdk4gp', {"user":"    'ell4xZlOUxIwbSTdk4gp',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'ell4xZlOUxIwbSTdk4gp',"}}
[info] >      'YrwVdr1vr3KGqtXP3bqX', {"user":"    'YrwVdr1vr3KGqtXP3bqX',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'YrwVdr1vr3KGqtXP3bqX',"}}
[info] >      '7owH6eTfGSYqaRpgIsQE', {"user":"    '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7owH6eTfGSYqaRpgIsQE',"}}
[info] >      'icSTOLwTaC6WYgWjsgDk', {"user":"    'icSTOLwTaC6WYgWjsgDk',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'icSTOLwTaC6WYgWjsgDk',"}}
[info] >      'BtniJTurDAn0saHHbEGF', {"user":"    'BtniJTurDAn0saHHbEGF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'BtniJTurDAn0saHHbEGF',"}}
[info] >      'wFSF3LbHQwPYy6A0pu94', {"user":"    'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'wFSF3LbHQwPYy6A0pu94',"}}
[info] >      'fNgkVoyHni01zmozumbK', {"user":"    'fNgkVoyHni01zmozumbK',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'fNgkVoyHni01zmozumbK',"}}
[info] >      'C6FceATDCJxVtji42Q3D', {"user":"    'C6FceATDCJxVtji42Q3D',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'C6FceATDCJxVtji42Q3D',"}}
[info] >      'M5Pahb6Vi1rdq7KZB5LF', {"user":"    'M5Pahb6Vi1rdq7KZB5LF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'M5Pahb6Vi1rdq7KZB5LF',"}}
[info] >      'oXnGAP4V78vilhIVpIQN', {"user":"    'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'oXnGAP4V78vilhIVpIQN',"}}
[info] >      'NpS3CdrpHvPZClMMDeFc', {"user":"    'NpS3CdrpHvPZClMMDeFc',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'NpS3CdrpHvPZClMMDeFc',"}}
[info] >      'TlS4g9PWKN1wNBtjHEoq', {"user":"    'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >      'rb2n32HuXJlFOk44cAkJ', {"user":"    'rb2n32HuXJlFOk44cAkJ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'rb2n32HuXJlFOk44cAkJ',"}}
[info] >      'UWkNKuXggt70Oe4bkzcx', {"user":"    'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'UWkNKuXggt70Oe4bkzcx',"}}
[info] >      'JRAQxnj2u4vmHNuPHz3W' {"user":"    'JRAQxnj2u4vmHNuPHz3W'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'JRAQxnj2u4vmHNuPHz3W'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  Processing farm selection for editing... {"user":"Processing farm selection for editing...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Processing farm selection for editing..."}}
[debug] [2025-07-25T12:17:57.635Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 525.5369ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 525.5369ms"}}
[debug] [2025-07-25T12:17:57.641Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: IDLE"}}
[debug] [2025-07-25T12:17:57.642Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-25T12:17:57.643Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-25T12:17:57.644Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[info] >  Selected farm ID for editing: Give me all detail i want to edit all fields {"user":"Selected farm ID for editing: Give me all detail i want to edit all fields","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Selected farm ID for editing: Give me all detail i want to edit all fields"}}
[debug] [2025-07-25T12:21:28.488Z] File D:\Sherry\projects\apps\mcp-server\functions\index.js changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File D:\\Sherry\\projects\\apps\\mcp-server\\functions\\index.js changed, reloading triggers"}}
[debug] [2025-07-25T12:21:29.547Z] Validating nodejs source
[debug] [2025-07-25T12:21:35.072Z] > [functions] package.json contents: {
  "name": "functions",
  "description": "Cloud Functions for Firebase",
  "scripts": {
    "serve": "firebase emulators:start --only functions",
    "shell": "firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "index.js",
  "dependencies": {
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1",
    "axios": "^1.10.0",
    "cors": "^2.8.5",
    "dotenv": "^17.2.0",
    "express": "^4.18.2",
    "multer": "^1.4.5-lts.1",
    "openai": "^4.20.1",
    "uuid": "^9.0.1"
  },
  "devDependencies": {
    "firebase-functions-test": "^3.1.0"
  },
  "private": true
}
[debug] [2025-07-25T12:21:35.073Z] Building nodejs source
[debug] [2025-07-25T12:21:35.073Z] Failed to find version of module node: reached end of search path D:\Sherry\projects\apps\mcp-server\functions\node_modules
[info] +  functions: Using node@22 from host. 
[info] i  functions: Loaded environment variables from .env. 
[debug] [2025-07-25T12:21:35.092Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-25T12:21:35.119Z] Found firebase-functions binary at 'D:\Sherry\projects\apps\mcp-server\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8824

[info] [dotenv@17.2.0] injecting env (2) from .env (tip: 🔐 prevent building .env in docker: https://dotenvx.com/prebuild)

[info] OpenAI client initialized successfully

[error] !!  functions: Failed to load function definition from source: FirebaseError: User code failed to load. Cannot determine backend specification. Timeout after 10000. See https://firebase.google.com/docs/functions/tips#avoid_deployment_timeouts_during_initialization' {"metadata":{"emulator":{"name":"functions"},"message":"Failed to load function definition from source: FirebaseError: User code failed to load. Cannot determine backend specification. Timeout after 10000. See https://firebase.google.com/docs/functions/tips#avoid_deployment_timeouts_during_initialization'"}}
[debug] [2025-07-25T12:22:08.920Z] File D:\Sherry\projects\apps\mcp-server\functions\index.js changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File D:\\Sherry\\projects\\apps\\mcp-server\\functions\\index.js changed, reloading triggers"}}
[debug] [2025-07-25T12:22:09.932Z] Validating nodejs source
[debug] [2025-07-25T12:22:14.707Z] > [functions] package.json contents: {
  "name": "functions",
  "description": "Cloud Functions for Firebase",
  "scripts": {
    "serve": "firebase emulators:start --only functions",
    "shell": "firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "index.js",
  "dependencies": {
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1",
    "axios": "^1.10.0",
    "cors": "^2.8.5",
    "dotenv": "^17.2.0",
    "express": "^4.18.2",
    "multer": "^1.4.5-lts.1",
    "openai": "^4.20.1",
    "uuid": "^9.0.1"
  },
  "devDependencies": {
    "firebase-functions-test": "^3.1.0"
  },
  "private": true
}
[debug] [2025-07-25T12:22:14.707Z] Building nodejs source
[debug] [2025-07-25T12:22:14.707Z] Failed to find version of module node: reached end of search path D:\Sherry\projects\apps\mcp-server\functions\node_modules
[info] +  functions: Using node@22 from host. 
[info] i  functions: Loaded environment variables from .env. 
[debug] [2025-07-25T12:22:14.712Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-25T12:22:14.732Z] Found firebase-functions binary at 'D:\Sherry\projects\apps\mcp-server\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8579

[info] [dotenv@17.2.0] injecting env (2) from .env (tip: ⚙️  write to custom object with { processEnv: myObject })

[info] OpenAI client initialized successfully

[debug] [2025-07-25T12:22:18.313Z] Got response from /__/functions.yaml {"endpoints":{"animalApp":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","region":["australia-southeast1"],"labels":{},"httpsTrigger":{},"entryPoint":"animalApp"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] +  functions: Loaded functions definitions from source: animalApp. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: animalApp."}}
[debug] [2025-07-25T12:22:22.497Z] [worker-pool] Shutting down IDLE worker (australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] Shutting down IDLE worker (australia-southeast1-animalApp)"}}
[debug] [2025-07-25T12:22:22.498Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: FINISHING {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: FINISHING"}}
[debug] [2025-07-25T12:22:22.556Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: exited {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: exited"}}
[debug] [2025-07-25T12:22:22.556Z] [worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: FINISHED {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-600ce43a-49ed-43bd-befe-d91f3a21573a]: FINISHED"}}
[debug] [2025-07-25T12:22:56.192Z] File D:\Sherry\projects\apps\mcp-server\functions\index.js changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File D:\\Sherry\\projects\\apps\\mcp-server\\functions\\index.js changed, reloading triggers"}}
[debug] [2025-07-25T12:22:57.261Z] Validating nodejs source
[debug] [2025-07-25T12:23:01.603Z] > [functions] package.json contents: {
  "name": "functions",
  "description": "Cloud Functions for Firebase",
  "scripts": {
    "serve": "firebase emulators:start --only functions",
    "shell": "firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "index.js",
  "dependencies": {
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1",
    "axios": "^1.10.0",
    "cors": "^2.8.5",
    "dotenv": "^17.2.0",
    "express": "^4.18.2",
    "multer": "^1.4.5-lts.1",
    "openai": "^4.20.1",
    "uuid": "^9.0.1"
  },
  "devDependencies": {
    "firebase-functions-test": "^3.1.0"
  },
  "private": true
}
[debug] [2025-07-25T12:23:01.604Z] Building nodejs source
[debug] [2025-07-25T12:23:01.604Z] Failed to find version of module node: reached end of search path D:\Sherry\projects\apps\mcp-server\functions\node_modules
[info] +  functions: Using node@22 from host. 
[info] i  functions: Loaded environment variables from .env. 
[debug] [2025-07-25T12:23:01.611Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-25T12:23:01.628Z] Found firebase-functions binary at 'D:\Sherry\projects\apps\mcp-server\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8697

[info] [dotenv@17.2.0] injecting env (2) from .env (tip: 🛠️  run anywhere with `dotenvx run -- yourcommand`)

[info] OpenAI client initialized successfully

[debug] [2025-07-25T12:23:04.409Z] Got response from /__/functions.yaml {"endpoints":{"animalApp":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","region":["australia-southeast1"],"labels":{},"httpsTrigger":{},"entryPoint":"animalApp"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] +  functions: Loaded functions definitions from source: animalApp. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: animalApp."}}
[debug] [2025-07-25T12:35:28.622Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:35:28.621Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:35:28.622Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:35:28.621Z"],"workRunningCount":1}
[debug] [2025-07-25T12:35:28.623Z] Accepted request OPTIONS /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-25T12:35:28.632Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-25T12:35:28.632Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-25T12:35:28.633Z] [worker-pool] Cleaned up workers for australia-southeast1-animalApp: 1 --> 0 {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] Cleaned up workers for australia-southeast1-animalApp: 1 --> 0"}}
[info] i  functions: Loaded environment variables from .env. 
[debug] [2025-07-25T12:35:28.717Z] [worker-pool] addWorker(australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] addWorker(australia-southeast1-animalApp)"}}
[debug] [2025-07-25T12:35:28.723Z] [worker-pool] Adding worker with key australia-southeast1-animalApp, total=1 {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] Adding worker with key australia-southeast1-animalApp, total=1"}}
[debug] [2025-07-25T12:35:35.472Z] [runtime-status] [32184] Resolved module firebase-admin {"declared":true,"installed":true,"version":"12.7.0","resolution":"D:\\Sherry\\projects\\apps\\mcp-server\\functions\\node_modules\\firebase-admin\\lib\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [32184] Resolved module firebase-admin {\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"D:\\\\Sherry\\\\projects\\\\apps\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-admin\\\\lib\\\\index.js\"}"}}
[debug] [2025-07-25T12:35:35.477Z] [runtime-status] [32184] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.4.0","resolution":"D:\\Sherry\\projects\\apps\\mcp-server\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [32184] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.4.0\",\"resolution\":\"D:\\\\Sherry\\\\projects\\\\apps\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-07-25T12:35:35.478Z] [runtime-status] [32184] Outgoing network have been stubbed. [{"name":"http","status":"mocked"},{"name":"http","status":"mocked"},{"name":"https","status":"mocked"},{"name":"https","status":"mocked"},{"name":"net","status":"mocked"}] {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [32184] Outgoing network have been stubbed. [{\"name\":\"http\",\"status\":\"mocked\"},{\"name\":\"http\",\"status\":\"mocked\"},{\"name\":\"https\",\"status\":\"mocked\"},{\"name\":\"https\",\"status\":\"mocked\"},{\"name\":\"net\",\"status\":\"mocked\"}]"}}
[debug] [2025-07-25T12:35:35.481Z] [runtime-status] [32184] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.4.0","resolution":"D:\\Sherry\\projects\\apps\\mcp-server\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [32184] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.4.0\",\"resolution\":\"D:\\\\Sherry\\\\projects\\\\apps\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-07-25T12:35:39.339Z] [runtime-status] [32184] Checked functions.config() {"config":{}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [32184] Checked functions.config() {\"config\":{}}"}}
[debug] [2025-07-25T12:35:39.340Z] [runtime-status] [32184] firebase-functions has been stubbed. {"functionsResolution":{"declared":true,"installed":true,"version":"6.4.0","resolution":"D:\\Sherry\\projects\\apps\\mcp-server\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [32184] firebase-functions has been stubbed. {\"functionsResolution\":{\"declared\":true,\"installed\":true,\"version\":\"6.4.0\",\"resolution\":\"D:\\\\Sherry\\\\projects\\\\apps\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}}"}}
[debug] [2025-07-25T12:35:39.345Z] [runtime-status] [32184] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.4.0","resolution":"D:\\Sherry\\projects\\apps\\mcp-server\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [32184] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.4.0\",\"resolution\":\"D:\\\\Sherry\\\\projects\\\\apps\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-07-25T12:35:39.425Z] [runtime-status] [32184] Resolved module firebase-admin {"declared":true,"installed":true,"version":"12.7.0","resolution":"D:\\Sherry\\projects\\apps\\mcp-server\\functions\\node_modules\\firebase-admin\\lib\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [32184] Resolved module firebase-admin {\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"D:\\\\Sherry\\\\projects\\\\apps\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-admin\\\\lib\\\\index.js\"}"}}
[debug] [2025-07-25T12:35:39.455Z] [runtime-status] [32184] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.4.0","resolution":"D:\\Sherry\\projects\\apps\\mcp-server\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [32184] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.4.0\",\"resolution\":\"D:\\\\Sherry\\\\projects\\\\apps\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-07-25T12:35:39.458Z] [runtime-status] [32184] firebase-admin has been stubbed. {"adminResolution":{"declared":true,"installed":true,"version":"12.7.0","resolution":"D:\\Sherry\\projects\\apps\\mcp-server\\functions\\node_modules\\firebase-admin\\lib\\index.js"}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [32184] firebase-admin has been stubbed. {\"adminResolution\":{\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"D:\\\\Sherry\\\\projects\\\\apps\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-admin\\\\lib\\\\index.js\"}}"}}
[info] >  [dotenv@17.2.0] injecting env (0) from .env (tip: ⚙️  load multiple .env files with { path: ['.env.local', '.env'] }) {"user":"[dotenv@17.2.0] injecting env (0) from .env (tip: ⚙️  load multiple .env files with { path: ['.env.local', '.env'] })","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m [dotenv@17.2.0] injecting env (0) from .env (tip: ⚙️  load multiple .env files with { path: ['.env.local', '.env'] })"}}
*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
[info] >  OpenAI client initialized successfully {"user":"OpenAI client initialized successfully","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m OpenAI client initialized successfully"}}
[debug] [2025-07-25T12:35:41.512Z] [runtime-status] [32184] Functions runtime initialized. {"cwd":"D:\\Sherry\\projects\\apps\\mcp-server\\functions","node_version":"22.17.0"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [32184] Functions runtime initialized. {\"cwd\":\"D:\\\\Sherry\\\\projects\\\\apps\\\\mcp-server\\\\functions\",\"node_version\":\"22.17.0\"}"}}
[debug] [2025-07-25T12:35:41.513Z] [runtime-status] [32184] Listening to port: \\?\pipe\fire_emu_89c9dad2a6808068 {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [32184] Listening to port: \\\\?\\pipe\\fire_emu_89c9dad2a6808068"}}
[debug] [2025-07-25T12:35:41.639Z] [worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: IDLE"}}
[debug] [2025-07-25T12:35:41.640Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-25T12:35:41.641Z] [worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: BUSY"}}
[debug] [2025-07-25T12:35:41.658Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 17.278ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 17.278ms"}}
[debug] [2025-07-25T12:35:41.660Z] [worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: IDLE"}}
[debug] [2025-07-25T12:35:41.660Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-25T12:35:41.660Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-25T12:35:41.660Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:35:41.703Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:35:41.703Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:35:41.704Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:35:41.703Z"],"workRunningCount":1}
[debug] [2025-07-25T12:35:41.704Z] Accepted request POST /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-25T12:35:41.708Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-25T12:35:41.708Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-25T12:35:41.709Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-25T12:35:41.710Z] [worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: BUSY"}}
[info] >  🔁 Initializing routes... {"user":"🔁 Initializing routes...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔁 Initializing routes..."}}
[info] >  🔁 Routes Initialized Successfully... {"user":"🔁 Routes Initialized Successfully...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔁 Routes Initialized Successfully..."}}
[info] >  📨 Context action: edit_farm {"user":"📨 Context action: edit_farm","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: edit_farm"}}
[info] >  📨 Context action: { {"user":"📨 Context action: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: {"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    farms: [ {"user":"  farms: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farms: ["}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: '7owH6eTfGSYqaRpgIsQE', {"user":"      id: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >        name: 'Haven View.', {"user":"      name: 'Haven View.',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Haven View.',"}}
[info] >        location: [Object] {"user":"      location: [Object]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       location: [Object]"}}
[info] >      } {"user":"    }","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     }"}}
[info] >    ], {"user":"  ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ],"}}
[info] >    previousAnimalData: null, {"user":"  previousAnimalData: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   previousAnimalData: null,"}}
[info] >    previousFarmData: null, {"user":"  previousFarmData: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   previousFarmData: null,"}}
[info] >    defaultFarmTypeId: 'B2bye8PZBQYqscxXoVqZ', {"user":"  defaultFarmTypeId: 'B2bye8PZBQYqscxXoVqZ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   defaultFarmTypeId: 'B2bye8PZBQYqscxXoVqZ',"}}
[info] >    defaultStatusId: 'tEbttSFNlpr6gGm5y66l', {"user":"  defaultStatusId: 'tEbttSFNlpr6gGm5y66l',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   defaultStatusId: 'tEbttSFNlpr6gGm5y66l',"}}
[info] >    defaultSizeUnitId: 'ezVe7GdWq6SAxwaREyIw', {"user":"  defaultSizeUnitId: 'ezVe7GdWq6SAxwaREyIw',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   defaultSizeUnitId: 'ezVe7GdWq6SAxwaREyIw',"}}
[info] >    farmLocation: '', {"user":"  farmLocation: '',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farmLocation: '',"}}
[info] >    context: { {"user":"  context: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   context: {"}}
[info] >      action: 'edit_farm', {"user":"    action: 'edit_farm',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     action: 'edit_farm',"}}
[info] >      needsFarmSelection: true, {"user":"    needsFarmSelection: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     needsFarmSelection: true,"}}
[info] >      availableFarms: [ {"user":"    availableFarms: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     availableFarms: ["}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object] {"user":"      [Object], [Object]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object]"}}
[info] >      ] {"user":"    ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     ]"}}
[info] >    }, {"user":"  },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   },"}}
[info] >    prompt: 'Edit' {"user":"  prompt: 'Edit'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Edit'"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  ............................ { {"user":"............................ {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ............................ {"}}
[info] >    id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    email: '<EMAIL>', {"user":"  email: '<EMAIL>',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   email: '<EMAIL>',"}}
[info] >    role: 'owner', {"user":"  role: 'owner',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   role: 'owner',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    preferAudio: false, {"user":"  preferAudio: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   preferAudio: false,"}}
[info] >    offlineMode: false, {"user":"  offlineMode: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   offlineMode: false,"}}
[info] >    emailVerified: true, {"user":"  emailVerified: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   emailVerified: true,"}}
[info] >    createdAt: 1746969090959, {"user":"  createdAt: 1746969090959,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   createdAt: 1746969090959,"}}
[info] >    name: 'Dr. Javad Ahmed', {"user":"  name: 'Dr. Javad Ahmed',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   name: 'Dr. Javad Ahmed',"}}
[info] >    ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ], {"user":"  ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],"}}
[info] >    profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90', {"user":"  profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',"}}
[info] >    lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 }, {"user":"  lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },"}}
[info] >    updatedAt: 1753292227987, {"user":"  updatedAt: 1753292227987,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   updatedAt: 1753292227987,"}}
[info] >    assignedFarmIds: [ {"user":"  assignedFarmIds: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   assignedFarmIds: ["}}
[info] >      'ell4xZlOUxIwbSTdk4gp', {"user":"    'ell4xZlOUxIwbSTdk4gp',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'ell4xZlOUxIwbSTdk4gp',"}}
[info] >      'YrwVdr1vr3KGqtXP3bqX', {"user":"    'YrwVdr1vr3KGqtXP3bqX',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'YrwVdr1vr3KGqtXP3bqX',"}}
[info] >      '7owH6eTfGSYqaRpgIsQE', {"user":"    '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7owH6eTfGSYqaRpgIsQE',"}}
[info] >      'icSTOLwTaC6WYgWjsgDk', {"user":"    'icSTOLwTaC6WYgWjsgDk',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'icSTOLwTaC6WYgWjsgDk',"}}
[info] >      'BtniJTurDAn0saHHbEGF', {"user":"    'BtniJTurDAn0saHHbEGF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'BtniJTurDAn0saHHbEGF',"}}
[info] >      'wFSF3LbHQwPYy6A0pu94', {"user":"    'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'wFSF3LbHQwPYy6A0pu94',"}}
[info] >      'fNgkVoyHni01zmozumbK', {"user":"    'fNgkVoyHni01zmozumbK',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'fNgkVoyHni01zmozumbK',"}}
[info] >      'C6FceATDCJxVtji42Q3D', {"user":"    'C6FceATDCJxVtji42Q3D',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'C6FceATDCJxVtji42Q3D',"}}
[info] >      'M5Pahb6Vi1rdq7KZB5LF', {"user":"    'M5Pahb6Vi1rdq7KZB5LF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'M5Pahb6Vi1rdq7KZB5LF',"}}
[info] >      'oXnGAP4V78vilhIVpIQN', {"user":"    'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'oXnGAP4V78vilhIVpIQN',"}}
[info] >      'NpS3CdrpHvPZClMMDeFc', {"user":"    'NpS3CdrpHvPZClMMDeFc',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'NpS3CdrpHvPZClMMDeFc',"}}
[info] >      'TlS4g9PWKN1wNBtjHEoq', {"user":"    'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >      'rb2n32HuXJlFOk44cAkJ', {"user":"    'rb2n32HuXJlFOk44cAkJ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'rb2n32HuXJlFOk44cAkJ',"}}
[info] >      'UWkNKuXggt70Oe4bkzcx', {"user":"    'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'UWkNKuXggt70Oe4bkzcx',"}}
[info] >      'JRAQxnj2u4vmHNuPHz3W' {"user":"    'JRAQxnj2u4vmHNuPHz3W'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'JRAQxnj2u4vmHNuPHz3W'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[debug] [2025-07-25T12:35:51.947Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 10237.6955ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 10237.6955ms"}}
[debug] [2025-07-25T12:35:51.954Z] [worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: IDLE"}}
[debug] [2025-07-25T12:35:51.954Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-25T12:35:51.955Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-25T12:35:51.955Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[info] >  Processing farm selection for editing... {"user":"Processing farm selection for editing...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Processing farm selection for editing..."}}
[info] >  Selected farm ID for editing: Edit {"user":"Selected farm ID for editing: Edit","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Selected farm ID for editing: Edit"}}
[debug] [2025-07-25T12:36:19.464Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:36:19.464Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:36:19.465Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:36:19.464Z"],"workRunningCount":1}
[debug] [2025-07-25T12:36:19.465Z] Accepted request OPTIONS /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-25T12:36:19.472Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-25T12:36:19.473Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-25T12:36:19.473Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-25T12:36:19.476Z] [worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: BUSY"}}
[debug] [2025-07-25T12:36:19.486Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 10.6211ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 10.6211ms"}}
[debug] [2025-07-25T12:36:19.508Z] [worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: IDLE"}}
[debug] [2025-07-25T12:36:19.508Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-25T12:36:19.508Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-25T12:36:19.508Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:36:19.531Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:36:19.531Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:36:19.532Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:36:19.531Z"],"workRunningCount":1}
[debug] [2025-07-25T12:36:19.532Z] Accepted request POST /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-25T12:36:19.539Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-25T12:36:19.540Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-25T12:36:19.540Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-25T12:36:19.543Z] [worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: BUSY"}}
[info] >  ............................ { {"user":"............................ {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ............................ {"}}
[info] >    id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    email: '<EMAIL>', {"user":"  email: '<EMAIL>',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   email: '<EMAIL>',"}}
[info] >    role: 'owner', {"user":"  role: 'owner',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   role: 'owner',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    preferAudio: false, {"user":"  preferAudio: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   preferAudio: false,"}}
[info] >    offlineMode: false, {"user":"  offlineMode: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   offlineMode: false,"}}
[info] >    emailVerified: true, {"user":"  emailVerified: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   emailVerified: true,"}}
[info] >    createdAt: 1746969090959, {"user":"  createdAt: 1746969090959,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   createdAt: 1746969090959,"}}
[info] >    name: 'Dr. Javad Ahmed', {"user":"  name: 'Dr. Javad Ahmed',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   name: 'Dr. Javad Ahmed',"}}
[info] >    ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ], {"user":"  ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],"}}
[info] >    profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90', {"user":"  profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',"}}
[info] >    lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 }, {"user":"  lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },"}}
[info] >    updatedAt: 1753292227987, {"user":"  updatedAt: 1753292227987,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   updatedAt: 1753292227987,"}}
[info] >    assignedFarmIds: [ {"user":"  assignedFarmIds: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   assignedFarmIds: ["}}
[info] >      'ell4xZlOUxIwbSTdk4gp', {"user":"    'ell4xZlOUxIwbSTdk4gp',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'ell4xZlOUxIwbSTdk4gp',"}}
[info] >      'YrwVdr1vr3KGqtXP3bqX', {"user":"    'YrwVdr1vr3KGqtXP3bqX',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'YrwVdr1vr3KGqtXP3bqX',"}}
[info] >      '7owH6eTfGSYqaRpgIsQE', {"user":"    '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7owH6eTfGSYqaRpgIsQE',"}}
[info] >      'icSTOLwTaC6WYgWjsgDk', {"user":"    'icSTOLwTaC6WYgWjsgDk',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'icSTOLwTaC6WYgWjsgDk',"}}
[info] >      'BtniJTurDAn0saHHbEGF', {"user":"    'BtniJTurDAn0saHHbEGF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'BtniJTurDAn0saHHbEGF',"}}
[info] >      'wFSF3LbHQwPYy6A0pu94', {"user":"    'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'wFSF3LbHQwPYy6A0pu94',"}}
[info] >      'fNgkVoyHni01zmozumbK', {"user":"    'fNgkVoyHni01zmozumbK',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'fNgkVoyHni01zmozumbK',"}}
[info] >      'C6FceATDCJxVtji42Q3D', {"user":"    'C6FceATDCJxVtji42Q3D',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'C6FceATDCJxVtji42Q3D',"}}
[info] >      'M5Pahb6Vi1rdq7KZB5LF', {"user":"    'M5Pahb6Vi1rdq7KZB5LF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'M5Pahb6Vi1rdq7KZB5LF',"}}
[info] >      'oXnGAP4V78vilhIVpIQN', {"user":"    'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'oXnGAP4V78vilhIVpIQN',"}}
[info] >      'NpS3CdrpHvPZClMMDeFc', {"user":"    'NpS3CdrpHvPZClMMDeFc',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'NpS3CdrpHvPZClMMDeFc',"}}
[info] >      'TlS4g9PWKN1wNBtjHEoq', {"user":"    'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >      'rb2n32HuXJlFOk44cAkJ', {"user":"    'rb2n32HuXJlFOk44cAkJ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'rb2n32HuXJlFOk44cAkJ',"}}
[info] >      'UWkNKuXggt70Oe4bkzcx', {"user":"    'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'UWkNKuXggt70Oe4bkzcx',"}}
[info] >      'JRAQxnj2u4vmHNuPHz3W' {"user":"    'JRAQxnj2u4vmHNuPHz3W'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'JRAQxnj2u4vmHNuPHz3W'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[debug] [2025-07-25T12:36:21.014Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 1471.2514ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 1471.2514ms"}}
[debug] [2025-07-25T12:36:21.018Z] [worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: IDLE"}}
[debug] [2025-07-25T12:36:21.018Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-25T12:36:21.019Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-25T12:36:21.019Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[info] >  ✏️ GENERAL EDIT PATTERN MATCHED! {"user":"✏️ GENERAL EDIT PATTERN MATCHED!","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ✏️ GENERAL EDIT PATTERN MATCHED!"}}
[info] >  requestType edit_general {"user":"requestType edit_general","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m requestType edit_general"}}
[info] >  📨 Context received: null {"user":"📨 Context received: null","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context received: null"}}
[info] >  📨 Context action: undefined {"user":"📨 Context action: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: undefined"}}
[info] >  📨 Context needsAnimalSelection: undefined {"user":"📨 Context needsAnimalSelection: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context needsAnimalSelection: undefined"}}
[info] >  Processing general edit request - showing module selection... {"user":"Processing general edit request - showing module selection...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Processing general edit request - showing module selection..."}}
[debug] [2025-07-25T12:36:30.523Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:36:30.523Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:36:30.523Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:36:30.523Z"],"workRunningCount":1}
[debug] [2025-07-25T12:36:30.524Z] Accepted request OPTIONS /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-25T12:36:30.531Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-25T12:36:30.531Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-25T12:36:30.531Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-25T12:36:30.533Z] [worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: BUSY"}}
[debug] [2025-07-25T12:36:30.539Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 6.2647ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 6.2647ms"}}
[debug] [2025-07-25T12:36:30.541Z] [worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: IDLE"}}
[debug] [2025-07-25T12:36:30.541Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-25T12:36:30.541Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-25T12:36:30.542Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:36:30.576Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:36:30.576Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:36:30.577Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:36:30.576Z"],"workRunningCount":1}
[debug] [2025-07-25T12:36:30.577Z] Accepted request POST /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-25T12:36:30.585Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-25T12:36:30.585Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-25T12:36:30.585Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-25T12:36:30.592Z] [worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: BUSY"}}
[info] >  📨 Context action: select_edit_module {"user":"📨 Context action: select_edit_module","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: select_edit_module"}}
[info] >  📨 Context action: { {"user":"📨 Context action: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: {"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    farms: [ {"user":"  farms: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farms: ["}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: '7owH6eTfGSYqaRpgIsQE', {"user":"      id: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >        name: 'Haven View.', {"user":"      name: 'Haven View.',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Haven View.',"}}
[info] >        location: [Object] {"user":"      location: [Object]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       location: [Object]"}}
[info] >      } {"user":"    }","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     }"}}
[info] >    ], {"user":"  ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ],"}}
[info] >    previousAnimalData: null, {"user":"  previousAnimalData: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   previousAnimalData: null,"}}
[info] >    previousFarmData: null, {"user":"  previousFarmData: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   previousFarmData: null,"}}
[info] >    defaultFarmTypeId: 'B2bye8PZBQYqscxXoVqZ', {"user":"  defaultFarmTypeId: 'B2bye8PZBQYqscxXoVqZ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   defaultFarmTypeId: 'B2bye8PZBQYqscxXoVqZ',"}}
[info] >    defaultStatusId: 'tEbttSFNlpr6gGm5y66l', {"user":"  defaultStatusId: 'tEbttSFNlpr6gGm5y66l',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   defaultStatusId: 'tEbttSFNlpr6gGm5y66l',"}}
[info] >    defaultSizeUnitId: 'ezVe7GdWq6SAxwaREyIw', {"user":"  defaultSizeUnitId: 'ezVe7GdWq6SAxwaREyIw',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   defaultSizeUnitId: 'ezVe7GdWq6SAxwaREyIw',"}}
[info] >    farmLocation: '', {"user":"  farmLocation: '',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farmLocation: '',"}}
[info] >    context: { action: 'select_edit_module', needsModuleSelection: true }, {"user":"  context: { action: 'select_edit_module', needsModuleSelection: true },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   context: { action: 'select_edit_module', needsModuleSelection: true },"}}
[info] >    prompt: 'Edit farm' {"user":"  prompt: 'Edit farm'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Edit farm'"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  ............................ { {"user":"............................ {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ............................ {"}}
[info] >    id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    email: '<EMAIL>', {"user":"  email: '<EMAIL>',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   email: '<EMAIL>',"}}
[info] >    role: 'owner', {"user":"  role: 'owner',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   role: 'owner',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    preferAudio: false, {"user":"  preferAudio: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   preferAudio: false,"}}
[info] >    offlineMode: false, {"user":"  offlineMode: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   offlineMode: false,"}}
[info] >    emailVerified: true, {"user":"  emailVerified: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   emailVerified: true,"}}
[info] >    createdAt: 1746969090959, {"user":"  createdAt: 1746969090959,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   createdAt: 1746969090959,"}}
[info] >    name: 'Dr. Javad Ahmed', {"user":"  name: 'Dr. Javad Ahmed',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   name: 'Dr. Javad Ahmed',"}}
[info] >    ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ], {"user":"  ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],"}}
[info] >    profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90', {"user":"  profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',"}}
[info] >    lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 }, {"user":"  lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },"}}
[info] >    updatedAt: 1753292227987, {"user":"  updatedAt: 1753292227987,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   updatedAt: 1753292227987,"}}
[info] >    assignedFarmIds: [ {"user":"  assignedFarmIds: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   assignedFarmIds: ["}}
[info] >      'ell4xZlOUxIwbSTdk4gp', {"user":"    'ell4xZlOUxIwbSTdk4gp',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'ell4xZlOUxIwbSTdk4gp',"}}
[info] >      'YrwVdr1vr3KGqtXP3bqX', {"user":"    'YrwVdr1vr3KGqtXP3bqX',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'YrwVdr1vr3KGqtXP3bqX',"}}
[info] >      '7owH6eTfGSYqaRpgIsQE', {"user":"    '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7owH6eTfGSYqaRpgIsQE',"}}
[info] >      'icSTOLwTaC6WYgWjsgDk', {"user":"    'icSTOLwTaC6WYgWjsgDk',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'icSTOLwTaC6WYgWjsgDk',"}}
[info] >      'BtniJTurDAn0saHHbEGF', {"user":"    'BtniJTurDAn0saHHbEGF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'BtniJTurDAn0saHHbEGF',"}}
[info] >      'wFSF3LbHQwPYy6A0pu94', {"user":"    'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'wFSF3LbHQwPYy6A0pu94',"}}
[info] >      'fNgkVoyHni01zmozumbK', {"user":"    'fNgkVoyHni01zmozumbK',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'fNgkVoyHni01zmozumbK',"}}
[info] >      'C6FceATDCJxVtji42Q3D', {"user":"    'C6FceATDCJxVtji42Q3D',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'C6FceATDCJxVtji42Q3D',"}}
[info] >      'M5Pahb6Vi1rdq7KZB5LF', {"user":"    'M5Pahb6Vi1rdq7KZB5LF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'M5Pahb6Vi1rdq7KZB5LF',"}}
[info] >      'oXnGAP4V78vilhIVpIQN', {"user":"    'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'oXnGAP4V78vilhIVpIQN',"}}
[info] >      'NpS3CdrpHvPZClMMDeFc', {"user":"    'NpS3CdrpHvPZClMMDeFc',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'NpS3CdrpHvPZClMMDeFc',"}}
[info] >      'TlS4g9PWKN1wNBtjHEoq', {"user":"    'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >      'rb2n32HuXJlFOk44cAkJ', {"user":"    'rb2n32HuXJlFOk44cAkJ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'rb2n32HuXJlFOk44cAkJ',"}}
[info] >      'UWkNKuXggt70Oe4bkzcx', {"user":"    'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'UWkNKuXggt70Oe4bkzcx',"}}
[info] >      'JRAQxnj2u4vmHNuPHz3W' {"user":"    'JRAQxnj2u4vmHNuPHz3W'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'JRAQxnj2u4vmHNuPHz3W'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  ✏️ FARM EDIT PATTERN MATCHED! {"user":"✏️ FARM EDIT PATTERN MATCHED!","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ✏️ FARM EDIT PATTERN MATCHED!"}}
[info] >  requestType edit_farm {"user":"requestType edit_farm","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m requestType edit_farm"}}
[info] >  Processing farm edit request... {"user":"Processing farm edit request...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Processing farm edit request..."}}
[warn] !  External network resource requested!
   - URL: "https://api.openai.com/v1/chat/completions"
 - Be careful, this may be a production service. {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"External network resource requested!\n   - URL: \"https://api.openai.com/v1/chat/completions\"\n - Be careful, this may be a production service."}}
[info] >  Farm edit analysis: { {"user":"Farm edit analysis: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Farm edit analysis: {"}}
[info] >    isEditRequest: true, {"user":"  isEditRequest: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   isEditRequest: true,"}}
[info] >    action: 'edit_farm', {"user":"  action: 'edit_farm',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   action: 'edit_farm',"}}
[info] >    farmName: null, {"user":"  farmName: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farmName: null,"}}
[info] >    fieldToUpdate: null, {"user":"  fieldToUpdate: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   fieldToUpdate: null,"}}
[info] >    newValue: null, {"user":"  newValue: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   newValue: null,"}}
[info] >    confidence: 70, {"user":"  confidence: 70,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   confidence: 70,"}}
[info] >    language: 'en' {"user":"  language: 'en'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en'"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[debug] [2025-07-25T12:36:35.496Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 4905.3252ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 4905.3252ms"}}
[debug] [2025-07-25T12:36:35.502Z] [worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: IDLE"}}
[debug] [2025-07-25T12:36:35.504Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:36:35.505Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[debug] [2025-07-25T12:36:35.507Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[debug] [2025-07-25T12:36:35.508Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[debug] [2025-07-25T12:36:35.511Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[debug] [2025-07-25T12:36:35.514Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[debug] [2025-07-25T12:36:35.516Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[debug] [2025-07-25T12:36:35.518Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[debug] [2025-07-25T12:36:35.519Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-25T12:36:35.519Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-25T12:36:40.797Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:36:40.797Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:36:40.798Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:36:40.797Z"],"workRunningCount":1}
[debug] [2025-07-25T12:36:40.798Z] Accepted request OPTIONS /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-25T12:36:40.805Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-25T12:36:40.805Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-25T12:36:40.806Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-25T12:36:40.820Z] [worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: BUSY"}}
[debug] [2025-07-25T12:36:40.828Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 9.1454ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 9.1454ms"}}
[debug] [2025-07-25T12:36:40.839Z] [worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: IDLE"}}
[debug] [2025-07-25T12:36:40.839Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-25T12:36:40.839Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-25T12:36:40.840Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:36:40.915Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:36:40.915Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:36:40.915Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:36:40.915Z"],"workRunningCount":1}
[debug] [2025-07-25T12:36:40.916Z] Accepted request POST /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-25T12:36:40.922Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-25T12:36:40.922Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-25T12:36:40.922Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-25T12:36:40.924Z] [worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: BUSY"}}
[info] >  📨 Context action: edit_farm {"user":"📨 Context action: edit_farm","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: edit_farm"}}
[info] >  📨 Context action: { {"user":"📨 Context action: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: {"}}
[info] >    prompt: 'Selected farm: Farm_003', {"user":"  prompt: 'Selected farm: Farm_003',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Selected farm: Farm_003',"}}
[info] >    selectedFarmId: 'JRAQxnj2u4vmHNuPHz3W', {"user":"  selectedFarmId: 'JRAQxnj2u4vmHNuPHz3W',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   selectedFarmId: 'JRAQxnj2u4vmHNuPHz3W',"}}
[info] >    userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    context: { {"user":"  context: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   context: {"}}
[info] >      action: 'edit_farm', {"user":"    action: 'edit_farm',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     action: 'edit_farm',"}}
[info] >      needsFarmSelection: true, {"user":"    needsFarmSelection: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     needsFarmSelection: true,"}}
[info] >      availableFarms: [ {"user":"    availableFarms: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     availableFarms: ["}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object], {"user":"      [Object], [Object],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object],"}}
[info] >        [Object], [Object] {"user":"      [Object], [Object]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       [Object], [Object]"}}
[info] >      ] {"user":"    ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     ]"}}
[info] >    }, {"user":"  },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   },"}}
[info] >    farms: [ {"user":"  farms: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farms: ["}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: '7owH6eTfGSYqaRpgIsQE', {"user":"      id: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >        name: 'Haven View.', {"user":"      name: 'Haven View.',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Haven View.',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'BtniJTurDAn0saHHbEGF', {"user":"      id: 'BtniJTurDAn0saHHbEGF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'BtniJTurDAn0saHHbEGF',"}}
[info] >        name: 'Green Land..', {"user":"      name: 'Green Land..',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Green Land..',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'wFSF3LbHQwPYy6A0pu94', {"user":"      id: 'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'wFSF3LbHQwPYy6A0pu94',"}}
[info] >        name: 'Dairy House ', {"user":"      name: 'Dairy House ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Dairy House ',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'fNgkVoyHni01zmozumbK', {"user":"      id: 'fNgkVoyHni01zmozumbK',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'fNgkVoyHni01zmozumbK',"}}
[info] >        name: 'Octans Farm', {"user":"      name: 'Octans Farm',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Octans Farm',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'oXnGAP4V78vilhIVpIQN', {"user":"      id: 'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'oXnGAP4V78vilhIVpIQN',"}}
[info] >        name: 'Trade Farm', {"user":"      name: 'Trade Farm',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Trade Farm',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'NpS3CdrpHvPZClMMDeFc', {"user":"      id: 'NpS3CdrpHvPZClMMDeFc',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'NpS3CdrpHvPZClMMDeFc',"}}
[info] >        name: 'River view ', {"user":"      name: 'River view ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'River view ',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'TlS4g9PWKN1wNBtjHEoq', {"user":"      id: 'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >        name: 'Sabz Chara', {"user":"      name: 'Sabz Chara',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Sabz Chara',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'rb2n32HuXJlFOk44cAkJ', {"user":"      id: 'rb2n32HuXJlFOk44cAkJ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'rb2n32HuXJlFOk44cAkJ',"}}
[info] >        name: 'Farm_002', {"user":"      name: 'Farm_002',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Farm_002',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'UWkNKuXggt70Oe4bkzcx', {"user":"      id: 'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'UWkNKuXggt70Oe4bkzcx',"}}
[info] >        name: 'Farm_003', {"user":"      name: 'Farm_003',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Farm_003',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'JRAQxnj2u4vmHNuPHz3W', {"user":"      id: 'JRAQxnj2u4vmHNuPHz3W',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'JRAQxnj2u4vmHNuPHz3W',"}}
[info] >        name: 'Farm_003', {"user":"      name: 'Farm_003',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Farm_003',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      } {"user":"    }","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     }"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  ............................ { {"user":"............................ {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ............................ {"}}
[info] >    id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    email: '<EMAIL>', {"user":"  email: '<EMAIL>',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   email: '<EMAIL>',"}}
[info] >    role: 'owner', {"user":"  role: 'owner',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   role: 'owner',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    preferAudio: false, {"user":"  preferAudio: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   preferAudio: false,"}}
[info] >    offlineMode: false, {"user":"  offlineMode: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   offlineMode: false,"}}
[info] >    emailVerified: true, {"user":"  emailVerified: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   emailVerified: true,"}}
[info] >    createdAt: 1746969090959, {"user":"  createdAt: 1746969090959,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   createdAt: 1746969090959,"}}
[info] >    name: 'Dr. Javad Ahmed', {"user":"  name: 'Dr. Javad Ahmed',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   name: 'Dr. Javad Ahmed',"}}
[info] >    ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ], {"user":"  ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],"}}
[info] >    profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90', {"user":"  profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',"}}
[info] >    lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 }, {"user":"  lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },"}}
[info] >    updatedAt: 1753292227987, {"user":"  updatedAt: 1753292227987,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   updatedAt: 1753292227987,"}}
[info] >    assignedFarmIds: [ {"user":"  assignedFarmIds: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   assignedFarmIds: ["}}
[info] >      'ell4xZlOUxIwbSTdk4gp', {"user":"    'ell4xZlOUxIwbSTdk4gp',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'ell4xZlOUxIwbSTdk4gp',"}}
[info] >      'YrwVdr1vr3KGqtXP3bqX', {"user":"    'YrwVdr1vr3KGqtXP3bqX',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'YrwVdr1vr3KGqtXP3bqX',"}}
[info] >      '7owH6eTfGSYqaRpgIsQE', {"user":"    '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7owH6eTfGSYqaRpgIsQE',"}}
[info] >      'icSTOLwTaC6WYgWjsgDk', {"user":"    'icSTOLwTaC6WYgWjsgDk',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'icSTOLwTaC6WYgWjsgDk',"}}
[info] >      'BtniJTurDAn0saHHbEGF', {"user":"    'BtniJTurDAn0saHHbEGF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'BtniJTurDAn0saHHbEGF',"}}
[info] >      'wFSF3LbHQwPYy6A0pu94', {"user":"    'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'wFSF3LbHQwPYy6A0pu94',"}}
[info] >      'fNgkVoyHni01zmozumbK', {"user":"    'fNgkVoyHni01zmozumbK',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'fNgkVoyHni01zmozumbK',"}}
[info] >      'C6FceATDCJxVtji42Q3D', {"user":"    'C6FceATDCJxVtji42Q3D',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'C6FceATDCJxVtji42Q3D',"}}
[info] >      'M5Pahb6Vi1rdq7KZB5LF', {"user":"    'M5Pahb6Vi1rdq7KZB5LF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'M5Pahb6Vi1rdq7KZB5LF',"}}
[info] >      'oXnGAP4V78vilhIVpIQN', {"user":"    'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'oXnGAP4V78vilhIVpIQN',"}}
[info] >      'NpS3CdrpHvPZClMMDeFc', {"user":"    'NpS3CdrpHvPZClMMDeFc',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'NpS3CdrpHvPZClMMDeFc',"}}
[info] >      'TlS4g9PWKN1wNBtjHEoq', {"user":"    'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >      'rb2n32HuXJlFOk44cAkJ', {"user":"    'rb2n32HuXJlFOk44cAkJ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'rb2n32HuXJlFOk44cAkJ',"}}
[info] >      'UWkNKuXggt70Oe4bkzcx', {"user":"    'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'UWkNKuXggt70Oe4bkzcx',"}}
[info] >      'JRAQxnj2u4vmHNuPHz3W' {"user":"    'JRAQxnj2u4vmHNuPHz3W'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'JRAQxnj2u4vmHNuPHz3W'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  Processing farm selection for editing... {"user":"Processing farm selection for editing...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Processing farm selection for editing..."}}
[info] >  Selected farm ID for editing: JRAQxnj2u4vmHNuPHz3W {"user":"Selected farm ID for editing: JRAQxnj2u4vmHNuPHz3W","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Selected farm ID for editing: JRAQxnj2u4vmHNuPHz3W"}}
[debug] [2025-07-25T12:36:41.414Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 490.8011ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 490.8011ms"}}
[debug] [2025-07-25T12:36:41.416Z] [worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: IDLE"}}
[debug] [2025-07-25T12:36:41.417Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-25T12:36:41.417Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-25T12:36:41.417Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:38:08.859Z] File D:\Sherry\projects\apps\mcp-server\functions\index.js changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File D:\\Sherry\\projects\\apps\\mcp-server\\functions\\index.js changed, reloading triggers"}}
[debug] [2025-07-25T12:38:09.872Z] Validating nodejs source
[debug] [2025-07-25T12:38:17.440Z] > [functions] package.json contents: {
  "name": "functions",
  "description": "Cloud Functions for Firebase",
  "scripts": {
    "serve": "firebase emulators:start --only functions",
    "shell": "firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "index.js",
  "dependencies": {
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1",
    "axios": "^1.10.0",
    "cors": "^2.8.5",
    "dotenv": "^17.2.0",
    "express": "^4.18.2",
    "multer": "^1.4.5-lts.1",
    "openai": "^4.20.1",
    "uuid": "^9.0.1"
  },
  "devDependencies": {
    "firebase-functions-test": "^3.1.0"
  },
  "private": true
}
[debug] [2025-07-25T12:38:17.440Z] Building nodejs source
[debug] [2025-07-25T12:38:17.440Z] Failed to find version of module node: reached end of search path D:\Sherry\projects\apps\mcp-server\functions\node_modules
[info] +  functions: Using node@22 from host. 
[info] i  functions: Loaded environment variables from .env. 
[debug] [2025-07-25T12:38:17.443Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-25T12:38:17.467Z] Found firebase-functions binary at 'D:\Sherry\projects\apps\mcp-server\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8238

[info] [dotenv@17.2.0] injecting env (2) from .env (tip: 🛠️  run anywhere with `dotenvx run -- yourcommand`)

[debug] [2025-07-25T12:38:21.415Z] Got response from /__/functions.yaml {"endpoints":{"animalApp":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","region":["australia-southeast1"],"labels":{},"httpsTrigger":{},"entryPoint":"animalApp"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] +  functions: Loaded functions definitions from source: animalApp. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: animalApp."}}
[debug] [2025-07-25T12:38:25.550Z] [worker-pool] Shutting down IDLE worker (australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] Shutting down IDLE worker (australia-southeast1-animalApp)"}}
[debug] [2025-07-25T12:38:25.550Z] [worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: FINISHING {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: FINISHING"}}
[debug] [2025-07-25T12:38:25.616Z] [worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: exited {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: exited"}}
[debug] [2025-07-25T12:38:25.617Z] [worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: FINISHED {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-23c11ad6-83e6-4dd5-9a22-6dc074ef8f27]: FINISHED"}}
[debug] [2025-07-25T12:40:46.738Z] File D:\Sherry\projects\apps\mcp-server\functions\controller\ai.js changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File D:\\Sherry\\projects\\apps\\mcp-server\\functions\\controller\\ai.js changed, reloading triggers"}}
[debug] [2025-07-25T12:40:47.749Z] Validating nodejs source
[debug] [2025-07-25T12:40:53.053Z] > [functions] package.json contents: {
  "name": "functions",
  "description": "Cloud Functions for Firebase",
  "scripts": {
    "serve": "firebase emulators:start --only functions",
    "shell": "firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "index.js",
  "dependencies": {
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1",
    "axios": "^1.10.0",
    "cors": "^2.8.5",
    "dotenv": "^17.2.0",
    "express": "^4.18.2",
    "multer": "^1.4.5-lts.1",
    "openai": "^4.20.1",
    "uuid": "^9.0.1"
  },
  "devDependencies": {
    "firebase-functions-test": "^3.1.0"
  },
  "private": true
}
[debug] [2025-07-25T12:40:53.054Z] Building nodejs source
[debug] [2025-07-25T12:40:53.054Z] Failed to find version of module node: reached end of search path D:\Sherry\projects\apps\mcp-server\functions\node_modules
[info] +  functions: Using node@22 from host. 
[info] i  functions: Loaded environment variables from .env. 
[debug] [2025-07-25T12:40:53.058Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-25T12:40:53.078Z] Found firebase-functions binary at 'D:\Sherry\projects\apps\mcp-server\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8859

[info] [dotenv@17.2.0] injecting env (2) from .env (tip: ⚙️  specify custom .env file path with { path: '/custom/path/.env' })

[debug] [2025-07-25T12:41:02.338Z] Got response from /__/functions.yaml {"endpoints":{"animalApp":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","region":["australia-southeast1"],"labels":{},"httpsTrigger":{},"entryPoint":"animalApp"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] +  functions: Loaded functions definitions from source: animalApp. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: animalApp."}}
[debug] [2025-07-25T12:41:10.836Z] File D:\Sherry\projects\apps\mcp-server\functions\controller\ai.js changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File D:\\Sherry\\projects\\apps\\mcp-server\\functions\\controller\\ai.js changed, reloading triggers"}}
[debug] [2025-07-25T12:41:11.839Z] Validating nodejs source
[debug] [2025-07-25T12:41:17.231Z] > [functions] package.json contents: {
  "name": "functions",
  "description": "Cloud Functions for Firebase",
  "scripts": {
    "serve": "firebase emulators:start --only functions",
    "shell": "firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "index.js",
  "dependencies": {
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1",
    "axios": "^1.10.0",
    "cors": "^2.8.5",
    "dotenv": "^17.2.0",
    "express": "^4.18.2",
    "multer": "^1.4.5-lts.1",
    "openai": "^4.20.1",
    "uuid": "^9.0.1"
  },
  "devDependencies": {
    "firebase-functions-test": "^3.1.0"
  },
  "private": true
}
[debug] [2025-07-25T12:41:17.232Z] Building nodejs source
[debug] [2025-07-25T12:41:17.232Z] Failed to find version of module node: reached end of search path D:\Sherry\projects\apps\mcp-server\functions\node_modules
[info] +  functions: Using node@22 from host. 
[info] i  functions: Loaded environment variables from .env. 
[debug] [2025-07-25T12:41:17.239Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-25T12:41:17.297Z] Found firebase-functions binary at 'D:\Sherry\projects\apps\mcp-server\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8917

[info] [dotenv@17.2.0] injecting env (2) from .env (tip: ⚙️  override existing env vars with { override: true })

[debug] [2025-07-25T12:41:20.284Z] Got response from /__/functions.yaml {"endpoints":{"animalApp":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","region":["australia-southeast1"],"labels":{},"httpsTrigger":{},"entryPoint":"animalApp"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] +  functions: Loaded functions definitions from source: animalApp. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: animalApp."}}
[debug] [2025-07-25T12:42:58.742Z] File D:\Sherry\projects\apps\mcp-server\functions\controller\ai.js changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File D:\\Sherry\\projects\\apps\\mcp-server\\functions\\controller\\ai.js changed, reloading triggers"}}
[debug] [2025-07-25T12:42:59.756Z] Validating nodejs source
[debug] [2025-07-25T12:43:04.515Z] > [functions] package.json contents: {
  "name": "functions",
  "description": "Cloud Functions for Firebase",
  "scripts": {
    "serve": "firebase emulators:start --only functions",
    "shell": "firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "index.js",
  "dependencies": {
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1",
    "axios": "^1.10.0",
    "cors": "^2.8.5",
    "dotenv": "^17.2.0",
    "express": "^4.18.2",
    "multer": "^1.4.5-lts.1",
    "openai": "^4.20.1",
    "uuid": "^9.0.1"
  },
  "devDependencies": {
    "firebase-functions-test": "^3.1.0"
  },
  "private": true
}
[debug] [2025-07-25T12:43:04.515Z] Building nodejs source
[debug] [2025-07-25T12:43:04.516Z] Failed to find version of module node: reached end of search path D:\Sherry\projects\apps\mcp-server\functions\node_modules
[info] +  functions: Using node@22 from host. 
[info] i  functions: Loaded environment variables from .env. 
[debug] [2025-07-25T12:43:04.522Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-25T12:43:04.543Z] Found firebase-functions binary at 'D:\Sherry\projects\apps\mcp-server\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8578

[info] [dotenv@17.2.0] injecting env (2) from .env (tip: ⚙️  specify custom .env file path with { path: '/custom/path/.env' })

[debug] [2025-07-25T12:43:07.304Z] Got response from /__/functions.yaml {"endpoints":{"animalApp":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","region":["australia-southeast1"],"labels":{},"httpsTrigger":{},"entryPoint":"animalApp"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] +  functions: Loaded functions definitions from source: animalApp. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: animalApp."}}
[debug] [2025-07-25T12:43:14.651Z] File D:\Sherry\projects\apps\mcp-server\functions\controller\ai.js changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File D:\\Sherry\\projects\\apps\\mcp-server\\functions\\controller\\ai.js changed, reloading triggers"}}
[debug] [2025-07-25T12:43:15.685Z] Validating nodejs source
[debug] [2025-07-25T12:43:21.575Z] > [functions] package.json contents: {
  "name": "functions",
  "description": "Cloud Functions for Firebase",
  "scripts": {
    "serve": "firebase emulators:start --only functions",
    "shell": "firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "index.js",
  "dependencies": {
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1",
    "axios": "^1.10.0",
    "cors": "^2.8.5",
    "dotenv": "^17.2.0",
    "express": "^4.18.2",
    "multer": "^1.4.5-lts.1",
    "openai": "^4.20.1",
    "uuid": "^9.0.1"
  },
  "devDependencies": {
    "firebase-functions-test": "^3.1.0"
  },
  "private": true
}
[debug] [2025-07-25T12:43:21.578Z] Building nodejs source
[debug] [2025-07-25T12:43:21.580Z] Failed to find version of module node: reached end of search path D:\Sherry\projects\apps\mcp-server\functions\node_modules
[info] +  functions: Using node@22 from host. 
[info] i  functions: Loaded environment variables from .env. 
[debug] [2025-07-25T12:43:21.592Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-25T12:43:21.665Z] Found firebase-functions binary at 'D:\Sherry\projects\apps\mcp-server\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8357

[info] [dotenv@17.2.0] injecting env (2) from .env (tip: ⚙️  write to custom object with { processEnv: myObject })

[debug] [2025-07-25T12:43:24.353Z] Got response from /__/functions.yaml {"endpoints":{"animalApp":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","region":["australia-southeast1"],"labels":{},"httpsTrigger":{},"entryPoint":"animalApp"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] +  functions: Loaded functions definitions from source: animalApp. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: animalApp."}}
[debug] [2025-07-25T12:43:30.177Z] File D:\Sherry\projects\apps\mcp-server\functions\controller\ai.js changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File D:\\Sherry\\projects\\apps\\mcp-server\\functions\\controller\\ai.js changed, reloading triggers"}}
[debug] [2025-07-25T12:43:31.198Z] Validating nodejs source
[debug] [2025-07-25T12:43:36.921Z] > [functions] package.json contents: {
  "name": "functions",
  "description": "Cloud Functions for Firebase",
  "scripts": {
    "serve": "firebase emulators:start --only functions",
    "shell": "firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "index.js",
  "dependencies": {
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1",
    "axios": "^1.10.0",
    "cors": "^2.8.5",
    "dotenv": "^17.2.0",
    "express": "^4.18.2",
    "multer": "^1.4.5-lts.1",
    "openai": "^4.20.1",
    "uuid": "^9.0.1"
  },
  "devDependencies": {
    "firebase-functions-test": "^3.1.0"
  },
  "private": true
}
[debug] [2025-07-25T12:43:36.921Z] Building nodejs source
[debug] [2025-07-25T12:43:36.922Z] Failed to find version of module node: reached end of search path D:\Sherry\projects\apps\mcp-server\functions\node_modules
[info] +  functions: Using node@22 from host. 
[info] i  functions: Loaded environment variables from .env. 
[debug] [2025-07-25T12:43:36.930Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-25T12:43:36.962Z] Found firebase-functions binary at 'D:\Sherry\projects\apps\mcp-server\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8771

[info] [dotenv@17.2.0] injecting env (2) from .env (tip: 🔐 encrypt with dotenvx: https://dotenvx.com)

[debug] [2025-07-25T12:43:41.131Z] Got response from /__/functions.yaml {"endpoints":{"animalApp":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","region":["australia-southeast1"],"labels":{},"httpsTrigger":{},"entryPoint":"animalApp"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] +  functions: Loaded functions definitions from source: animalApp. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: animalApp."}}
[debug] [2025-07-25T12:43:52.026Z] File D:\Sherry\projects\apps\mcp-server\functions\controller\ai.js changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File D:\\Sherry\\projects\\apps\\mcp-server\\functions\\controller\\ai.js changed, reloading triggers"}}
[debug] [2025-07-25T12:43:53.036Z] Validating nodejs source
[debug] [2025-07-25T12:43:58.091Z] > [functions] package.json contents: {
  "name": "functions",
  "description": "Cloud Functions for Firebase",
  "scripts": {
    "serve": "firebase emulators:start --only functions",
    "shell": "firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "index.js",
  "dependencies": {
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1",
    "axios": "^1.10.0",
    "cors": "^2.8.5",
    "dotenv": "^17.2.0",
    "express": "^4.18.2",
    "multer": "^1.4.5-lts.1",
    "openai": "^4.20.1",
    "uuid": "^9.0.1"
  },
  "devDependencies": {
    "firebase-functions-test": "^3.1.0"
  },
  "private": true
}
[debug] [2025-07-25T12:43:58.092Z] Building nodejs source
[debug] [2025-07-25T12:43:58.092Z] Failed to find version of module node: reached end of search path D:\Sherry\projects\apps\mcp-server\functions\node_modules
[info] +  functions: Using node@22 from host. 
[info] i  functions: Loaded environment variables from .env. 
[debug] [2025-07-25T12:43:58.096Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-25T12:43:58.130Z] Found firebase-functions binary at 'D:\Sherry\projects\apps\mcp-server\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8618

[info] [dotenv@17.2.0] injecting env (2) from .env (tip: ⚙️  override existing env vars with { override: true })

[debug] [2025-07-25T12:44:00.747Z] Got response from /__/functions.yaml {"endpoints":{"animalApp":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","region":["australia-southeast1"],"labels":{},"httpsTrigger":{},"entryPoint":"animalApp"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] +  functions: Loaded functions definitions from source: animalApp. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: animalApp."}}
[debug] [2025-07-25T12:45:41.950Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:45:41.950Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-25T12:45:41.951Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-25T12:45:41.950Z"],"workRunningCount":1}
[debug] [2025-07-25T12:45:41.951Z] Accepted request OPTIONS /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-25T12:45:41.963Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-25T12:45:41.965Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-25T12:45:41.965Z] [worker-pool] Cleaned up workers for australia-southeast1-animalApp: 1 --> 0 {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] Cleaned up workers for australia-southeast1-animalApp: 1 --> 0"}}
[info] i  functions: Loaded environment variables from .env. 
[debug] [2025-07-25T12:45:42.061Z] [worker-pool] addWorker(australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] addWorker(australia-southeast1-animalApp)"}}
[debug] [2025-07-25T12:45:42.064Z] [worker-pool] Adding worker with key australia-southeast1-animalApp, total=1 {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] Adding worker with key australia-southeast1-animalApp, total=1"}}
