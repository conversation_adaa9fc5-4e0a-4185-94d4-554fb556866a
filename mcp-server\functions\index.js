require('dotenv').config(); // Add this at the top
const { onRequest } = require("firebase-functions/v2/https");
var admin = require("firebase-admin");
var serviceAccount = require("./config/firebase-service-account.json");
 
admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    storageBucket: process.env.STORAGE_BUCKET || 'kissandost-9570f.firebasestorage.app' // Replace with your actual bucket
});

const express = require('express');
const cors = require('cors');
const app = express();

// Middleware
app.use(cors());

// Lazy route mounting
let routesInitialized = false;
function initRoutes() {
    if (!routesInitialized) {
        console.log("🔁 Initializing routes...");
        require('./routes/openAi')(app);
        routesInitialized = true;
        console.log("🔁 Routes Initialized Successfully...");
    }
}
 
// Middleware to ensure routes are initialized on first request
app.use((req, res, next) => {
    initRoutes();
    next();
});
 exports.animalApp = onRequest(
    {
        region: "australia-southeast1"
    },
    app
);